
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { LanguageProvider } from "./contexts/LanguageContext";
import Navigation from "./components/Navigation";
import Home from "./pages/Home";
import ScientificCalculator from "./pages/ScientificCalculator";
import FinancialCalculator from "./pages/FinancialCalculator";
import ConversionCalculator from "./pages/ConversionCalculator";
import HealthCalculator from "./pages/HealthCalculator";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <LanguageProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <div className="min-h-screen bg-gray-50">
            <Navigation />
            <Routes>
              {/* English routes (default) */}
              <Route path="/" element={<Home />} />
              <Route path="/scientific" element={<ScientificCalculator />} />
              <Route path="/financial" element={<FinancialCalculator />} />
              <Route path="/conversion" element={<ConversionCalculator />} />
              <Route path="/health" element={<HealthCalculator />} />
              
              {/* French routes */}
              <Route path="/fr" element={<Home />} />
              <Route path="/fr/scientifique" element={<ScientificCalculator />} />
              <Route path="/fr/financiere" element={<FinancialCalculator />} />
              <Route path="/fr/conversion" element={<ConversionCalculator />} />
              <Route path="/fr/sante" element={<HealthCalculator />} />
              
              {/* Spanish routes */}
              <Route path="/es" element={<Home />} />
              <Route path="/es/cientifica" element={<ScientificCalculator />} />
              <Route path="/es/financiera" element={<FinancialCalculator />} />
              <Route path="/es/conversion" element={<ConversionCalculator />} />
              <Route path="/es/salud" element={<HealthCalculator />} />
              
              {/* Arabic routes */}
              <Route path="/ar" element={<Home />} />
              <Route path="/ar/علمية" element={<ScientificCalculator />} />
              <Route path="/ar/مالية" element={<FinancialCalculator />} />
              <Route path="/ar/تحويل" element={<ConversionCalculator />} />
              <Route path="/ar/صحة" element={<HealthCalculator />} />
              
              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        </BrowserRouter>
      </LanguageProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
