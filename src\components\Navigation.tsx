import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Calculator, Globe, Menu, X } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useState } from 'react';

const Navigation = () => {
  const { language, setLanguage, translations, isRTL } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const getLocalizedPath = (path: string, targetLang: string = language) => {
    if (targetLang === 'en') return path;
    
    // Map English paths to localized paths
    const pathMappings: Record<string, Record<string, string>> = {
      '/': {
        fr: '/fr',
        es: '/es',
        ar: '/ar'
      },
      '/scientific': {
        fr: '/fr/scientifique',
        es: '/es/cientifica',
        ar: '/ar/علمية'
      },
      '/financial': {
        fr: '/fr/financiere',
        es: '/es/financiera',
        ar: '/ar/مالية'
      },
      '/conversion': {
        fr: '/fr/conversion',
        es: '/es/conversion',
        ar: '/ar/تحويل'
      },
      '/health': {
        fr: '/fr/sante',
        es: '/es/salud',
        ar: '/ar/صحة'
      }
    };

    return pathMappings[path]?.[targetLang] || path;
  };

  const getCurrentBasePath = () => {
    const pathname = location.pathname;
    
    // Map current path back to base English path
    const reverseMappings: Record<string, string> = {
      '/fr': '/',
      '/fr/scientifique': '/scientific',
      '/fr/financiere': '/financial',
      '/fr/conversion': '/conversion',
      '/fr/sante': '/health',
      '/es': '/',
      '/es/cientifica': '/scientific',
      '/es/financiera': '/financial',
      '/es/conversion': '/conversion',
      '/es/salud': '/health',
      '/ar': '/',
      '/ar/علمية': '/scientific',
      '/ar/مالية': '/financial',
      '/ar/تحويل': '/conversion',
      '/ar/صحة': '/health'
    };

    return reverseMappings[pathname] || pathname;
  };

  const isActivePath = (path: string) => {
    const localizedPath = getLocalizedPath(path);
    return location.pathname === localizedPath;
  };

  const navItems = [
    { key: 'home', path: '/', icon: null },
    { key: 'scientific', path: '/scientific', icon: null },
    { key: 'financial', path: '/financial', icon: null },
    { key: 'conversion', path: '/conversion', icon: null },
    { key: 'health', path: '/health', icon: null },
  ];

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  ];

  const handleLanguageChange = (newLang: string) => {
    const currentBasePath = getCurrentBasePath();
    const newPath = getLocalizedPath(currentBasePath, newLang);
    
    setLanguage(newLang as any);
    navigate(newPath);
  };

  return (
    <nav className={`bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to={getLocalizedPath('/')} className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
              <div className="bg-gradient-to-r from-calculator-primary to-calculator-secondary p-2 rounded-lg">
                <Calculator className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-calculator-primary to-calculator-secondary bg-clip-text text-transparent">
                PowerCalc
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className={`hidden md:flex items-center space-x-8 ${isRTL ? 'space-x-reverse' : ''}`}>
            {navItems.map((item) => (
              <Link
                key={item.key}
                to={getLocalizedPath(item.path)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActivePath(item.path)
                    ? 'text-calculator-primary bg-calculator-primary/10'
                    : 'text-gray-700 hover:text-calculator-primary hover:bg-gray-100'
                }`}
              >
                {translations[`nav.${item.key}`]}
              </Link>
            ))}

            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Globe className="h-4 w-4" />
                  {languages.find(lang => lang.code === language)?.flag}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-white border border-gray-200 shadow-lg z-50">
                {languages.map((lang) => (
                  <DropdownMenuItem
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`flex items-center space-x-2 cursor-pointer hover:bg-gray-50 ${isRTL ? 'space-x-reverse text-right' : ''}`}
                  >
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200/50">
            <div className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.key}
                  to={getLocalizedPath(item.path)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${isRTL ? 'text-right' : 'text-left'} ${
                    isActivePath(item.path)
                      ? 'text-calculator-primary bg-calculator-primary/10'
                      : 'text-gray-700 hover:text-calculator-primary hover:bg-gray-100'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {translations[`nav.${item.key}`]}
                </Link>
              ))}
              
              <div className="pt-2 border-t border-gray-200/50">
                <div className="grid grid-cols-2 gap-2">
                  {languages.map((lang) => (
                    <Button
                      key={lang.code}
                      variant={language === lang.code ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        handleLanguageChange(lang.code);
                        setIsMobileMenuOpen(false);
                      }}
                      className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}
                    >
                      <span>{lang.flag}</span>
                      <span className="text-xs">{lang.name}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
