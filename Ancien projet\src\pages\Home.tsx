
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowR<PERSON>, Calculator, TrendingUp, Repeat, Heart, CheckCircle, Users, Globe, Zap, Shield } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import BasicCalculator from '../components/BasicCalculator';

const Home = () => {
  const { language, translations, isRTL } = useLanguage();

  const getLocalizedPath = (path: string) => {
    if (language === 'en') return path;
    
    const pathMappings: Record<string, Record<string, string>> = {
      '/scientific': {
        fr: '/fr/scientifique',
        es: '/es/cientifica',
        ar: '/ar/علمية'
      },
      '/financial': {
        fr: '/fr/financiere',
        es: '/es/financiera',
        ar: '/ar/مالية'
      },
      '/conversion': {
        fr: '/fr/conversion',
        es: '/es/conversion',
        ar: '/ar/تحويل'
      },
      '/health': {
        fr: '/fr/sante',
        es: '/es/salud',
        ar: '/ar/صحة'
      }
    };

    return pathMappings[path]?.[language] || path;
  };

  const features = [
    {
      icon: Calculator,
      titleKey: 'home.features.scientific',
      descKey: 'home.features.scientific_desc',
      path: '/scientific',
      gradient: 'from-blue-500 to-purple-600',
      bgGradient: 'from-blue-50 to-purple-50',
    },
    {
      icon: TrendingUp,
      titleKey: 'home.features.financial',
      descKey: 'home.features.financial_desc',
      path: '/financial',
      gradient: 'from-green-500 to-emerald-600',
      bgGradient: 'from-green-50 to-emerald-50',
    },
    {
      icon: Repeat,
      titleKey: 'home.features.conversion',
      descKey: 'home.features.conversion_desc',
      path: '/conversion',
      gradient: 'from-orange-500 to-red-600',
      bgGradient: 'from-orange-50 to-red-50',
    },
    {
      icon: Heart,
      titleKey: 'home.features.health',
      descKey: 'home.features.health_desc',
      path: '/health',
      gradient: 'from-pink-500 to-rose-600',
      bgGradient: 'from-pink-50 to-rose-50',
    },
  ];

  const benefits = [
    { icon: CheckCircle, key: 'home.seo.benefit1' },
    { icon: Globe, key: 'home.seo.benefit2' },
    { icon: Users, key: 'home.seo.benefit3' },
    { icon: Zap, key: 'home.seo.benefit4' },
    { icon: Shield, key: 'home.seo.benefit5' },
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Hero Section with Calculator */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-calculator-primary/10 via-calculator-secondary/10 to-calculator-accent/10" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <div className={`${isRTL ? 'lg:text-right' : 'lg:text-left'} text-center lg:text-left`}>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-calculator-primary via-calculator-secondary to-calculator-accent bg-clip-text text-transparent">
                  {translations['home.title']}
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-4 font-medium">
                {translations['home.subtitle']}
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                {translations['home.description']}
              </p>
              <Link to={getLocalizedPath('/scientific')}>
                <Button size="lg" className="bg-gradient-to-r from-calculator-primary to-calculator-secondary hover:from-calculator-primary/90 hover:to-calculator-secondary/90 transition-all duration-300 transform hover:scale-105 shadow-lg">
                  {translations['home.get_started']}
                  <ArrowRight className={`h-5 w-5 ${isRTL ? 'rotate-180 mr-2 ml-0' : 'ml-2'}`} />
                </Button>
              </Link>
            </div>
            
            {/* Right side - Calculator */}
            <div className="flex justify-center lg:justify-end">
              <div className="w-full max-w-sm">
                <BasicCalculator />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SEO Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-16">
          <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {translations['home.seo.intro']}
            </p>
          </div>
          
          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {translations['home.seo.benefits_title']}
              </h3>
              <div className="space-y-4">
                {benefits.map((benefit, index) => {
                  const Icon = benefit.icon;
                  return (
                    <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className="h-6 w-6 text-calculator-primary mt-1 flex-shrink-0" />
                      <p className="text-gray-700">{translations[benefit.key]}</p>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-calculator-primary/5 to-calculator-secondary/5 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {translations['home.quick_calc']}
              </h3>
              <p className="text-gray-700 mb-4">
                {translations['home.try_calculator']}
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div>• Basic arithmetic operations</div>
                <div>• Advanced mathematical functions</div>
                <div>• Memory storage capabilities</div>
                <div>• History tracking</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {translations['home.features.title']}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-calculator-primary to-calculator-secondary mx-auto rounded-full" />
        </div>

        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Link key={index} to={getLocalizedPath(feature.path)} className="group">
                <Card className={`h-full transition-all duration-300 hover:shadow-xl hover:-translate-y-2 bg-gradient-to-br ${feature.bgGradient} border-0 overflow-hidden relative`}>
                  <div className="absolute inset-0 bg-white/50 backdrop-blur-sm" />
                  <CardHeader className="relative">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 ${isRTL ? 'mr-auto' : ''}`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-calculator-primary group-hover:to-calculator-secondary group-hover:bg-clip-text transition-all duration-300">
                      {translations[feature.titleKey]}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="relative">
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {translations[feature.descKey]}
                    </CardDescription>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-calculator-primary via-calculator-secondary to-calculator-accent py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h3 className="text-3xl font-bold text-white mb-4">
            {translations['home.cta.title']}
          </h3>
          <p className="text-xl text-white/90 mb-8">
            {translations['home.cta.description']}
          </p>
          <div className={`flex flex-wrap justify-center gap-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            {features.map((feature, index) => (
              <Link key={index} to={getLocalizedPath(feature.path)}>
                <Button 
                  variant="secondary" 
                  size="lg"
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30 hover:border-white/50 backdrop-blur-sm transition-all duration-300 hover:scale-105"
                >
                  {translations[feature.titleKey]}
                </Button>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
