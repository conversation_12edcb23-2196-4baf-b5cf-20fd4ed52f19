# PowerCalc - Professional Calculator Suite

A comprehensive Next.js application featuring advanced calculators with full internationalization support.

## 🚀 Migration to Next.js

This project has been successfully migrated from React + Vite to Next.js 15 with the following improvements:

### ✨ New Features

- **Next.js 15** with App Router for better performance and SEO
- **Full Internationalization (i18n)** with next-intl
- **Translated URLs** for each language (e.g., `/fr/scientifique`, `/es/cientifica`, `/ar/علمية`)
- **RTL Support** for Arabic language
- **Server-Side Rendering (SSR)** for better SEO and performance
- **Automatic locale detection** based on browser preferences
- **Optimized metadata** for each page and language

### 🌍 Supported Languages

- **English** (en) - Default
- **French** (fr) - Français
- **Spanish** (es) - Español
- **Arabic** (ar) - العربية (with RTL support)

### 📱 Calculator Types

1. **Scientific Calculator** (`/scientific`)
   - Advanced mathematical functions
   - Trigonometry, logarithms, and more
   - Memory functions and calculation history

2. **Financial Calculator** (`/financial`)
   - Loan calculations
   - Mortgage calculator
   - Investment planning
   - Compound interest

3. **Unit Converter** (`/conversion`)
   - Length, weight, temperature
   - Area, volume, speed
   - Energy, pressure, power

4. **Health Calculator** (`/health`)
   - BMI calculator
   - BMR calculator
   - Calorie calculator

## 🛠️ Technology Stack

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **next-intl** - Internationalization
- **Radix UI** - Accessible UI components
- **Lucide React** - Icons
- **React Hook Form** - Form handling
- **Zod** - Schema validation

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd calculator
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Build for Production

```bash
npm run build
npm start
```

## 🌐 Internationalization

### URL Structure

Each language has its own URL structure:

- English: `/`, `/scientific`, `/financial`, `/conversion`, `/health`
- French: `/fr`, `/fr/scientifique`, `/fr/financiere`, `/fr/conversion`, `/fr/sante`
- Spanish: `/es`, `/es/cientifica`, `/es/financiera`, `/es/conversion`, `/es/salud`
- Arabic: `/ar`, `/ar/علمية`, `/ar/مالية`, `/ar/تحويل`, `/ar/صحة`

### RTL Support

Arabic language automatically enables RTL (Right-to-Left) layout with proper text direction and mirrored layouts.

## 📝 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
