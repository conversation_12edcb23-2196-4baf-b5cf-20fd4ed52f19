"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing.js":
/*!************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar defineRouting = __webpack_require__(/*! ./routing/defineRouting.js */ \"(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\");\n\n\n\nexports.defineRouting = defineRouting.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELG9CQUFvQixtQkFBTyxDQUFDLDRHQUE0Qjs7OztBQUl4RCxxQkFBcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxkZXZlbG9wbWVudFxccm91dGluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBkZWZpbmVSb3V0aW5nID0gcmVxdWlyZSgnLi9yb3V0aW5nL2RlZmluZVJvdXRpbmcuanMnKTtcblxuXG5cbmV4cG9ydHMuZGVmaW5lUm91dGluZyA9IGRlZmluZVJvdXRpbmcuZGVmYXVsdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/defineRouting.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction defineRouting(config) {\n  return config;\n}\n\nexports[\"default\"] = defineRouting;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0Q7QUFDQTtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGRlZmluZVJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG5mdW5jdGlvbiBkZWZpbmVSb3V0aW5nKGNvbmZpZykge1xuICByZXR1cm4gY29uZmlnO1xufVxuXG5leHBvcnRzLmRlZmF1bHQgPSBkZWZpbmVSb3V0aW5nO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXF92aXJ0dWFsXFxfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\");\nfunction t(t){const{config:n,...r}=(0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((function(){return (0,_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()}),t);function u(e){return()=>{throw new Error(\"`\".concat(e,\"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.\"))}}return{...r,usePathname:u(\"usePathname\"),useRouter:u(\"useRouter\")}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBGLGNBQWMsTUFBTSxjQUFjLENBQUMsZ0ZBQUMsYUFBYSxPQUFPLCtEQUFDLEdBQUcsS0FBSyxjQUFjLFdBQVcsNkpBQTZKLE9BQU8sNERBQWlGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxuYXZpZ2F0aW9uXFxyZWFjdC1zZXJ2ZXJcXGNyZWF0ZU5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGUgZnJvbVwiLi4vc2hhcmVkL2NyZWF0ZVNoYXJlZE5hdmlnYXRpb25GbnMuanNcIjtpbXBvcnQgbyBmcm9tXCIuL2dldFNlcnZlckxvY2FsZS5qc1wiO2Z1bmN0aW9uIHQodCl7Y29uc3R7Y29uZmlnOm4sLi4ucn09ZSgoZnVuY3Rpb24oKXtyZXR1cm4gbygpfSksdCk7ZnVuY3Rpb24gdShlKXtyZXR1cm4oKT0+e3Rocm93IG5ldyBFcnJvcihcImBcIi5jb25jYXQoZSxcImAgaXMgbm90IHN1cHBvcnRlZCBpbiBTZXJ2ZXIgQ29tcG9uZW50cy4gWW91IGNhbiB1c2UgdGhpcyBob29rIGlmIHlvdSBjb252ZXJ0IHRoZSBjYWxsaW5nIGNvbXBvbmVudCB0byBhIENsaWVudCBDb21wb25lbnQuXCIpKX19cmV0dXJuey4uLnIsdXNlUGF0aG5hbWU6dShcInVzZVBhdGhuYW1lXCIpLHVzZVJvdXRlcjp1KFwidXNlUm91dGVyXCIpfX1leHBvcnR7dCBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nasync function r(){return(await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()).locale}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRCxtQkFBbUIsYUFBYSw2RUFBQyxXQUFnQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcbmF2aWdhdGlvblxccmVhY3Qtc2VydmVyXFxnZXRTZXJ2ZXJMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGUgZnJvbVwiLi4vLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanNcIjthc3luYyBmdW5jdGlvbiByKCl7cmV0dXJuKGF3YWl0IGUoKSkubG9jYWxlfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\navigation\\\\shared\\\\BaseLink.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\node_modules\\next-intl\\dist\\esm\\navigation\\shared\\BaseLink.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\navigation\\\\shared\\\\LegacyBaseLink.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\node_modules\\next-intl\\dist\\esm\\navigation\\shared\\LegacyBaseLink.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ h)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction h(h,y){const j=(0,_routing_config_js__WEBPACK_IMPORTED_MODULE_2__.receiveRoutingConfig)(y||{});(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.validateReceivedConfig)(j);const g=j.pathnames,v=\"as-needed\"===j.localePrefix.mode&&j.domains||void 0;function q(o,a){let n,l,u,{href:f,locale:s,...p}=o;\"object\"==typeof f?(n=f.pathname,u=f.query,l=f.params):n=f;const d=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isLocalizableHref)(f),y=h(),q=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isPromise)(y)?(0,react__WEBPACK_IMPORTED_MODULE_1__.use)(y):y,x=d?L({locale:s||q,href:null==g?n:{pathname:n,params:l}},null!=s||v||void 0):n;return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__[\"extends\"])({ref:a,defaultLocale:j.defaultLocale,href:\"object\"==typeof f?{...f,pathname:x}:x,locale:s,localeCookie:j.localeCookie,unprefixed:v&&d?{domains:j.domains.reduce(((e,o)=>(e[o.domain]=o.defaultLocale,e)),{}),pathname:L({locale:q,href:null==g?{pathname:n,query:u}:{pathname:n,query:u,params:l}},!1)}:void 0},p))}const x=(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(q);function L(e,o){const{href:a,locale:t}=e;let n;return null==g?\"object\"==typeof a?(n=a.pathname,a.query&&(n+=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.serializeSearchParams)(a.query))):n=a:n=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.compileLocalizedPathname)({locale:t,...(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.normalizeNameOrNameWithParams)(a),pathnames:j.pathnames}),(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.applyPathnamePrefix)(n,t,j,e.domain,o)}function b(e){return function(o){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];return e(L(o,o.domain?void 0:v),...t)}}const k=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect),P=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);return{config:j,Link:x,redirect:k,permanentRedirect:P,getPathname:L}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function f(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function f(e){let n=o[e];return n||(n=e),n}function s(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return s(f(n))}{const{pathname:e,...t}=n;return{...t,pathname:s(f(e))}}}function s(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:f}=a.localePrefix;let s;if(void 0!==c)s=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===f)s=!0;else if(\"as-needed\"===f){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\")}s=e!==n}return s?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\nasync function i(i){let{locale:n,now:s,timeZone:m,...c}=i;return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({locale:null!=n?n:await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),now:null!=s?s:await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),timeZone:null!=m?m:await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()},c))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1TLG9CQUFvQixJQUFJLCtCQUErQixHQUFHLE9BQU8sMERBQWUsQ0FBQyx5RUFBQyxDQUFDLGdGQUFDLEVBQUUsdUJBQXVCLDZFQUFDLHVCQUF1QiwwRUFBQyw0QkFBNEIsK0VBQUMsR0FBRyxLQUEwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxccmVhY3Qtc2VydmVyXFxOZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCByIGZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi4vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIjtpbXBvcnQgbyBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qc1wiO2ltcG9ydCBsIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzXCI7aW1wb3J0IGEgZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qc1wiO2FzeW5jIGZ1bmN0aW9uIGkoaSl7bGV0e2xvY2FsZTpuLG5vdzpzLHRpbWVab25lOm0sLi4uY309aTtyZXR1cm4gci5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm51bGwhPW4/bjphd2FpdCBvKCksbm93Om51bGwhPXM/czphd2FpdCBsKCksdGltZVpvbmU6bnVsbCE9bT9tOmF3YWl0IGEoKX0sYykpfWV4cG9ydHtpIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/routing/config.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/routing/config.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveLocaleCookie: () => (/* binding */ o),\n/* harmony export */   receiveLocalePrefixConfig: () => (/* binding */ l),\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ e)\n/* harmony export */ });\nfunction e(e){var t,n;return{...e,localePrefix:l(e.localePrefix),localeCookie:o(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(n=e.alternateLinks)||void 0===n||n}}function o(e){return!(null!=e&&!e)&&{name:\"NEXT_LOCALE\",maxAge:31536e3,sameSite:\"lax\",...\"object\"==typeof e&&e}}function l(e){return\"object\"==typeof e?e:{mode:e||\"always\"}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLGNBQWMsUUFBUSxPQUFPLDBMQUEwTCxjQUFjLHVCQUF1QiwyRUFBMkUsY0FBYyw0QkFBNEIsa0JBQTRHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxyb3V0aW5nXFxjb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZShlKXt2YXIgdCxuO3JldHVybnsuLi5lLGxvY2FsZVByZWZpeDpsKGUubG9jYWxlUHJlZml4KSxsb2NhbGVDb29raWU6byhlLmxvY2FsZUNvb2tpZSksbG9jYWxlRGV0ZWN0aW9uOm51bGw9PT0odD1lLmxvY2FsZURldGVjdGlvbil8fHZvaWQgMD09PXR8fHQsYWx0ZXJuYXRlTGlua3M6bnVsbD09PShuPWUuYWx0ZXJuYXRlTGlua3MpfHx2b2lkIDA9PT1ufHxufX1mdW5jdGlvbiBvKGUpe3JldHVybiEobnVsbCE9ZSYmIWUpJiZ7bmFtZTpcIk5FWFRfTE9DQUxFXCIsbWF4QWdlOjMxNTM2ZTMsc2FtZVNpdGU6XCJsYXhcIiwuLi5cIm9iamVjdFwiPT10eXBlb2YgZSYmZX19ZnVuY3Rpb24gbChlKXtyZXR1cm5cIm9iamVjdFwiPT10eXBlb2YgZT9lOnttb2RlOmV8fFwiYWx3YXlzXCJ9fWV4cG9ydHtvIGFzIHJlY2VpdmVMb2NhbGVDb29raWUsbCBhcyByZWNlaXZlTG9jYWxlUHJlZml4Q29uZmlnLGUgYXMgcmVjZWl2ZVJvdXRpbmdDb25maWd9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){const e=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(e)?await e:e}));const s=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){let t;try{t=(await i()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)||void 0}catch(t){if(t instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===t.digest){const e=new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:t});throw e.digest=t.digest,e}throw t}return t}));async function a(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||await s()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVQLFFBQVEsNENBQUMsbUJBQW1CLFFBQVEscURBQUMsR0FBRyxPQUFPLDJEQUFDLGNBQWMsR0FBRyxRQUFRLDRDQUFDLG1CQUFtQixNQUFNLElBQUksa0JBQWtCLG9FQUFDLFVBQVUsU0FBUywwREFBMEQsK1VBQStVLFFBQVEsRUFBRSwwQkFBMEIsUUFBUSxTQUFTLEdBQUcsbUJBQW1CLE9BQU8sOEVBQUMsY0FBNEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNlcnZlclxccmVhY3Qtc2VydmVyXFxSZXF1ZXN0TG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtoZWFkZXJzIGFzIHR9ZnJvbVwibmV4dC9oZWFkZXJzXCI7aW1wb3J0e2NhY2hlIGFzIGV9ZnJvbVwicmVhY3RcIjtpbXBvcnR7SEVBREVSX0xPQ0FMRV9OQU1FIGFzIG59ZnJvbVwiLi4vLi4vc2hhcmVkL2NvbnN0YW50cy5qc1wiO2ltcG9ydHtpc1Byb21pc2UgYXMgcn1mcm9tXCIuLi8uLi9zaGFyZWQvdXRpbHMuanNcIjtpbXBvcnR7Z2V0Q2FjaGVkUmVxdWVzdExvY2FsZSBhcyBvfWZyb21cIi4vUmVxdWVzdExvY2FsZUNhY2hlLmpzXCI7Y29uc3QgaT1lKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IGU9dCgpO3JldHVybiByKGUpP2F3YWl0IGU6ZX0pKTtjb25zdCBzPWUoKGFzeW5jIGZ1bmN0aW9uKCl7bGV0IHQ7dHJ5e3Q9KGF3YWl0IGkoKSkuZ2V0KG4pfHx2b2lkIDB9Y2F0Y2godCl7aWYodCBpbnN0YW5jZW9mIEVycm9yJiZcIkRZTkFNSUNfU0VSVkVSX1VTQUdFXCI9PT10LmRpZ2VzdCl7Y29uc3QgZT1uZXcgRXJyb3IoXCJVc2FnZSBvZiBuZXh0LWludGwgQVBJcyBpbiBTZXJ2ZXIgQ29tcG9uZW50cyBjdXJyZW50bHkgb3B0cyBpbnRvIGR5bmFtaWMgcmVuZGVyaW5nLiBUaGlzIGxpbWl0YXRpb24gd2lsbCBldmVudHVhbGx5IGJlIGxpZnRlZCwgYnV0IGFzIGEgc3RvcGdhcCBzb2x1dGlvbiwgeW91IGNhbiB1c2UgdGhlIGBzZXRSZXF1ZXN0TG9jYWxlYCBBUEkgdG8gZW5hYmxlIHN0YXRpYyByZW5kZXJpbmcsIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9nZXR0aW5nLXN0YXJ0ZWQvYXBwLXJvdXRlci93aXRoLWkxOG4tcm91dGluZyNzdGF0aWMtcmVuZGVyaW5nXCIse2NhdXNlOnR9KTt0aHJvdyBlLmRpZ2VzdD10LmRpZ2VzdCxlfXRocm93IHR9cmV0dXJuIHR9KSk7YXN5bmMgZnVuY3Rpb24gYSgpe3JldHVybiBvKCl8fGF3YWl0IHMoKX1leHBvcnR7YSBhcyBnZXRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nconst n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(){return{locale:void 0}}));function t(){return n().locale}function c(o){n().locale=o}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEIsUUFBUSw0Q0FBQyxhQUFhLE9BQU8sZUFBZSxHQUFHLGFBQWEsa0JBQWtCLGNBQWMsYUFBNkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNlcnZlclxccmVhY3Qtc2VydmVyXFxSZXF1ZXN0TG9jYWxlQ2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIG99ZnJvbVwicmVhY3RcIjtjb25zdCBuPW8oKGZ1bmN0aW9uKCl7cmV0dXJue2xvY2FsZTp2b2lkIDB9fSkpO2Z1bmN0aW9uIHQoKXtyZXR1cm4gbigpLmxvY2FsZX1mdW5jdGlvbiBjKG8pe24oKS5sb2NhbGU9b31leHBvcnR7dCBhcyBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlLGMgYXMgc2V0Q2FjaGVkUmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_2__.cache)((function(){let n;try{n=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)}catch(e){throw e instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===e.digest?new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:e}):e}return n||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()),n}));function s(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdQLFFBQVEsNENBQUMsYUFBYSxNQUFNLElBQUksRUFBRSxxREFBQyxPQUFPLG9FQUFDLEVBQUUsU0FBUyxtWUFBbVksUUFBUSxJQUFJLG9QQUFvUCx5REFBQyxNQUFNLEdBQUcsYUFBYSxPQUFPLDhFQUFDLFFBQXNDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcUmVxdWVzdExvY2FsZUxlZ2FjeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7aGVhZGVycyBhcyBlfWZyb21cIm5leHQvaGVhZGVyc1wiO2ltcG9ydHtub3RGb3VuZCBhcyB0fWZyb21cIm5leHQvbmF2aWdhdGlvblwiO2ltcG9ydHtjYWNoZSBhcyBufWZyb21cInJlYWN0XCI7aW1wb3J0e0hFQURFUl9MT0NBTEVfTkFNRSBhcyBvfWZyb21cIi4uLy4uL3NoYXJlZC9jb25zdGFudHMuanNcIjtpbXBvcnR7Z2V0Q2FjaGVkUmVxdWVzdExvY2FsZSBhcyByfWZyb21cIi4vUmVxdWVzdExvY2FsZUNhY2hlLmpzXCI7Y29uc3QgaT1uKChmdW5jdGlvbigpe2xldCBuO3RyeXtuPWUoKS5nZXQobyl9Y2F0Y2goZSl7dGhyb3cgZSBpbnN0YW5jZW9mIEVycm9yJiZcIkRZTkFNSUNfU0VSVkVSX1VTQUdFXCI9PT1lLmRpZ2VzdD9uZXcgRXJyb3IoXCJVc2FnZSBvZiBuZXh0LWludGwgQVBJcyBpbiBTZXJ2ZXIgQ29tcG9uZW50cyBjdXJyZW50bHkgb3B0cyBpbnRvIGR5bmFtaWMgcmVuZGVyaW5nLiBUaGlzIGxpbWl0YXRpb24gd2lsbCBldmVudHVhbGx5IGJlIGxpZnRlZCwgYnV0IGFzIGEgc3RvcGdhcCBzb2x1dGlvbiwgeW91IGNhbiB1c2UgdGhlIGBzZXRSZXF1ZXN0TG9jYWxlYCBBUEkgdG8gZW5hYmxlIHN0YXRpYyByZW5kZXJpbmcsIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9nZXR0aW5nLXN0YXJ0ZWQvYXBwLXJvdXRlci93aXRoLWkxOG4tcm91dGluZyNzdGF0aWMtcmVuZGVyaW5nXCIse2NhdXNlOmV9KTplfXJldHVybiBufHwoY29uc29sZS5lcnJvcihcIlxcblVuYWJsZSB0byBmaW5kIGBuZXh0LWludGxgIGxvY2FsZSBiZWNhdXNlIHRoZSBtaWRkbGV3YXJlIGRpZG4ndCBydW4gb24gdGhpcyByZXF1ZXN0LiBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3Mvcm91dGluZy9taWRkbGV3YXJlI3VuYWJsZS10by1maW5kLWxvY2FsZS4gVGhlIGBub3RGb3VuZCgpYCBmdW5jdGlvbiB3aWxsIGJlIGNhbGxlZCBhcyBhIHJlc3VsdC5cXG5cIiksdCgpKSxufSkpO2Z1bmN0aW9uIHMoKXtyZXR1cm4gcigpfHxpKCl9ZXhwb3J0e3MgYXMgZ2V0UmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ w)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./i18n/request.ts\");\nlet c=!1,u=!1;const f=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return new Date}));const d=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const m=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(t,n){if(\"function\"!=typeof t)throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\\n\");const o={get locale(){return u||(console.warn(\"\\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),u=!0),n||(0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)()},get requestLocale(){return n?Promise.resolve(n):(0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)()}};let r=t(o);(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(r)&&(r=await r);let s=r.locale;return s||(c||(console.error(\"\\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\\n\\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),c=!0),s=await o.requestLocale,s||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)())),{...r,locale:s,now:r.now||f(),timeZone:r.timeZone||d()}})),p=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createIntlFormatters),g=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createCache);const w=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(e){const t=await m(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"],e);return{...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_6__.initializeConfig)(t),_formatters:p(g())}}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst r=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(){const o=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();return Promise.resolve(o.locale)}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxtQkFBbUIsY0FBYyx5REFBQyxHQUFHLGlDQUFpQyxHQUF3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldExvY2FsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgb31mcm9tXCJyZWFjdFwiO2ltcG9ydCB0IGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgcj1vKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IG89YXdhaXQgdCgpO3JldHVybiBQcm9taXNlLnJlc29sdmUoby5sb2NhbGUpfSkpO2V4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getMessages.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction t(e){if(!e.messages)throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages\");return e.messages}const n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){return t(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(e))}));async function r(e){return n(null==e?void 0:e.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQsY0FBYywrSUFBK0ksa0JBQWtCLFFBQVEsNENBQUMsb0JBQW9CLGVBQWUseURBQUMsS0FBSyxHQUFHLG9CQUFvQixrQ0FBa0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRNZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBvIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7ZnVuY3Rpb24gdChlKXtpZighZS5tZXNzYWdlcyl0aHJvdyBuZXcgRXJyb3IoXCJObyBtZXNzYWdlcyBmb3VuZC4gSGF2ZSB5b3UgY29uZmlndXJlZCB0aGVtIGNvcnJlY3RseT8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbWVzc2FnZXNcIik7cmV0dXJuIGUubWVzc2FnZXN9Y29uc3Qgbj1lKChhc3luYyBmdW5jdGlvbihlKXtyZXR1cm4gdChhd2FpdCBvKGUpKX0pKTthc3luYyBmdW5jdGlvbiByKGUpe3JldHVybiBuKG51bGw9PWU/dm9pZCAwOmUubG9jYWxlKX1leHBvcnR7ciBhcyBkZWZhdWx0LHQgYXMgZ2V0TWVzc2FnZXNGcm9tQ29uZmlnfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(n){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now}));async function r(n){return t(null==n?void 0:n.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxvQkFBb0IsYUFBYSx5REFBQyxTQUFTLEdBQUcsb0JBQW9CLGtDQUF1RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldE5vdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgbn1mcm9tXCJyZWFjdFwiO2ltcG9ydCBvIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3QgdD1uKChhc3luYyBmdW5jdGlvbihuKXtyZXR1cm4oYXdhaXQgbyhuKSkubm93fSkpO2FzeW5jIGZ1bmN0aW9uIHIobil7cmV0dXJuIHQobnVsbD09bj92b2lkIDA6bi5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t){return t}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsY0FBYyxTQUE4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldFJlcXVlc3RDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gdCh0KXtyZXR1cm4gdH1leHBvcnR7dCBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst o=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(t){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone}));async function r(t){return o(null==t?void 0:t.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RCxRQUFRLDRDQUFDLG9CQUFvQixhQUFhLHlEQUFDLGNBQWMsR0FBRyxvQkFBb0Isa0NBQXVEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0VGltZVpvbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIHR9ZnJvbVwicmVhY3RcIjtpbXBvcnQgbiBmcm9tXCIuL2dldENvbmZpZy5qc1wiO2NvbnN0IG89dCgoYXN5bmMgZnVuY3Rpb24odCl7cmV0dXJuKGF3YWl0IG4odCkpLnRpbWVab25lfSkpO2FzeW5jIGZ1bmN0aW9uIHIodCl7cmV0dXJuIG8obnVsbD09dD92b2lkIDA6dC5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nvar s=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){let s,o;\"string\"==typeof e?s=e:e&&(o=e.locale,s=e.namespace);const r=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(o);return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_2__.createTranslator)({...r,namespace:s,messages:r.messages})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZHLE1BQU0sNENBQUMsb0JBQW9CLFFBQVEscURBQXFELGNBQWMseURBQUMsSUFBSSxPQUFPLCtEQUFDLEVBQUUscUNBQXFDLEVBQUUsR0FBd0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRUcmFuc2xhdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIGV9ZnJvbVwicmVhY3RcIjtpbXBvcnR7Y3JlYXRlVHJhbnNsYXRvciBhcyB0fWZyb21cInVzZS1pbnRsL2NvcmVcIjtpbXBvcnQgYSBmcm9tXCIuL2dldENvbmZpZy5qc1wiO3ZhciBzPWUoKGFzeW5jIGZ1bmN0aW9uKGUpe2xldCBzLG87XCJzdHJpbmdcIj09dHlwZW9mIGU/cz1lOmUmJihvPWUubG9jYWxlLHM9ZS5uYW1lc3BhY2UpO2NvbnN0IHI9YXdhaXQgYShvKTtyZXR1cm4gdCh7Li4ucixuYW1lc3BhY2U6cyxtZXNzYWdlczpyLm1lc3NhZ2VzfSl9KSk7ZXhwb3J0e3MgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\node_modules\\next-intl\\dist\\esm\\shared\\NextIntlClientProvider.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNoYXJlZFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(c,o);return(f||l)&&null!=c?e(t,c):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function o(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function c(n,t){const e=o(n),r=o(t);return s(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return\"/\"+n}function s(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function a(n){return n.includes(\"[[...\")}function p(n){return n.includes(\"[...\")}function h(n){return n.includes(\"[\")}function g(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}function v(n){return\"function\"==typeof n.then}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUEsa0JBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxkZXZlbG9wbWVudFxcX3ZpcnR1YWxcXF9yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgcmV0dXJuIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiA/IE9iamVjdC5hc3NpZ24uYmluZCgpIDogZnVuY3Rpb24gKG4pIHtcbiAgICBmb3IgKHZhciBlID0gMTsgZSA8IGFyZ3VtZW50cy5sZW5ndGg7IGUrKykge1xuICAgICAgdmFyIHQgPSBhcmd1bWVudHNbZV07XG4gICAgICBmb3IgKHZhciByIGluIHQpICh7fSkuaGFzT3duUHJvcGVydHkuY2FsbCh0LCByKSAmJiAobltyXSA9IHRbcl0pO1xuICAgIH1cbiAgICByZXR1cm4gbjtcbiAgfSwgX2V4dGVuZHMuYXBwbHkobnVsbCwgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0cy5leHRlbmRzID0gX2V4dGVuZHM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n\n\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return useIntl[k]; }\n\t});\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsWUFBWSxtQkFBTyxDQUFDLHNHQUF5QjtBQUM3QyxnQkFBZ0IsbUJBQU8sQ0FBQyw4R0FBNkI7QUFDckQsNkJBQTZCLG1CQUFPLENBQUMsNEhBQW9DO0FBQ3pFLGNBQWMsbUJBQU8sQ0FBQyw2REFBVTs7OztBQUloQyxvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLGlCQUFpQjtBQUNqQiw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLEVBQUU7QUFDRixDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXGluZGV4LnJlYWN0LWNsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBpbmRleCA9IHJlcXVpcmUoJy4vcmVhY3QtY2xpZW50L2luZGV4LmpzJyk7XG52YXIgdXNlTG9jYWxlID0gcmVxdWlyZSgnLi9yZWFjdC1jbGllbnQvdXNlTG9jYWxlLmpzJyk7XG52YXIgTmV4dEludGxDbGllbnRQcm92aWRlciA9IHJlcXVpcmUoJy4vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMnKTtcbnZhciB1c2VJbnRsID0gcmVxdWlyZSgndXNlLWludGwnKTtcblxuXG5cbmV4cG9ydHMudXNlRm9ybWF0dGVyID0gaW5kZXgudXNlRm9ybWF0dGVyO1xuZXhwb3J0cy51c2VUcmFuc2xhdGlvbnMgPSBpbmRleC51c2VUcmFuc2xhdGlvbnM7XG5leHBvcnRzLnVzZUxvY2FsZSA9IHVzZUxvY2FsZS5kZWZhdWx0O1xuZXhwb3J0cy5OZXh0SW50bENsaWVudFByb3ZpZGVyID0gTmV4dEludGxDbGllbnRQcm92aWRlci5kZWZhdWx0O1xuT2JqZWN0LmtleXModXNlSW50bCkuZm9yRWFjaChmdW5jdGlvbiAoaykge1xuXHRpZiAoayAhPT0gJ2RlZmF1bHQnICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgaykpIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBrLCB7XG5cdFx0ZW51bWVyYWJsZTogdHJ1ZSxcblx0XHRnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHVzZUludGxba107IH1cblx0fSk7XG59KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation.react-client.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar createSharedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createSharedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\");\nvar createLocalizedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createLocalizedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\");\nvar createNavigation = __webpack_require__(/*! ./navigation/react-client/createNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\");\n\n\n\nexports.createSharedPathnamesNavigation = createSharedPathnamesNavigation.default;\nexports.createLocalizedPathnamesNavigation = createLocalizedPathnamesNavigation.default;\nexports.createNavigation = createNavigation.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDLEVBQUUsYUFBYSxFQUFDOztBQUU3RCxzQ0FBc0MsbUJBQU8sQ0FBQyxnTEFBOEQ7QUFDNUcseUNBQXlDLG1CQUFPLENBQUMsc0xBQWlFO0FBQ2xILHVCQUF1QixtQkFBTyxDQUFDLGtKQUErQzs7OztBQUk5RSx1Q0FBdUM7QUFDdkMsMENBQTBDO0FBQzFDLHdCQUF3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGRldmVsb3BtZW50XFxuYXZpZ2F0aW9uLnJlYWN0LWNsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uID0gcmVxdWlyZSgnLi9uYXZpZ2F0aW9uL3JlYWN0LWNsaWVudC9jcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmpzJyk7XG52YXIgY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IHJlcXVpcmUoJy4vbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbi5qcycpO1xudmFyIGNyZWF0ZU5hdmlnYXRpb24gPSByZXF1aXJlKCcuL25hdmlnYXRpb24vcmVhY3QtY2xpZW50L2NyZWF0ZU5hdmlnYXRpb24uanMnKTtcblxuXG5cbmV4cG9ydHMuY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IGNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24uZGVmYXVsdDtcbmV4cG9ydHMuY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IGNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24uZGVmYXVsdDtcbmV4cG9ydHMuY3JlYXRlTmF2aWdhdGlvbiA9IGNyZWF0ZU5hdmlnYXRpb24uZGVmYXVsdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar LegacyBaseLink = __webpack_require__(/*! ../shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction ClientLink(_ref, ref) {\n  let {\n    locale,\n    localePrefix,\n    ...rest\n  } = _ref;\n  const defaultLocale = useLocale.default();\n  const finalLocale = locale || defaultLocale;\n  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);\n  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({\n    ref: ref,\n    locale: finalLocale,\n    localePrefixMode: localePrefix.mode,\n    prefix: prefix\n  }, rest));\n}\n\n/**\n * Wraps `next/link` and prefixes the `href` with the current locale if\n * necessary.\n *\n * @example\n * ```tsx\n * import {Link} from 'next-intl';\n *\n * // When the user is on `/en`, the link will point to `/en/about`\n * <Link href=\"/about\">About</Link>\n *\n * // You can override the `locale` to switch to another language\n * <Link href=\"/\" locale=\"de\">Switch to German</Link>\n * ```\n *\n * Note that when a `locale` prop is passed to switch the locale, the `prefetch`\n * prop is not supported. This is because Next.js would prefetch the page and\n * the `set-cookie` response header would cause the locale cookie on the current\n * page to be overwritten before the user even decides to change the locale.\n */\nconst ClientLinkWithRef = /*#__PURE__*/React.forwardRef(ClientLink);\nClientLinkWithRef.displayName = 'ClientLink';\n\nexports[\"default\"] = ClientLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createLocalizedPathnamesNavigation(routing) {\n  const config$1 = config.receiveRoutingConfig(routing);\n  const localeCookie = config.receiveLocaleCookie(routing.localeCookie);\n  function useTypedLocale() {\n    const locale = useLocale.default();\n    const isValid = config$1.locales.includes(locale);\n    if (!isValid) {\n      throw new Error(\"Unknown locale encountered: \\\"\".concat(locale, \"\\\". Make sure to validate the locale in `i18n.ts`.\") );\n    }\n    return locale;\n  }\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    const defaultLocale = useTypedLocale();\n    const finalLocale = locale || defaultLocale;\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      href: utils.compileLocalizedPathname({\n        locale: finalLocale,\n        // @ts-expect-error -- This is ok\n        pathname: href,\n        // @ts-expect-error -- This is ok\n        params: typeof href === 'object' ? href.params : undefined,\n        pathnames: config$1.pathnames\n      }),\n      locale: locale,\n      localeCookie: localeCookie,\n      localePrefix: config$1.localePrefix\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function useRouter() {\n    const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);\n    const defaultLocale = useTypedLocale();\n    return React.useMemo(() => ({\n      ...baseRouter,\n      push(href) {\n        var _args$;\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale\n        });\n        return baseRouter.push(resolvedHref, ...args);\n      },\n      replace(href) {\n        var _args$2;\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale\n        });\n        return baseRouter.replace(resolvedHref, ...args);\n      },\n      prefetch(href) {\n        var _args$3;\n        for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n          args[_key5 - 1] = arguments[_key5];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale\n        });\n        return baseRouter.prefetch(resolvedHref, ...args);\n      }\n    }), [baseRouter, defaultLocale]);\n  }\n  function usePathname() {\n    const pathname = useBasePathname.default(config$1);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [locale, pathname]);\n  }\n  function getPathname(_ref2) {\n    let {\n      href,\n      locale\n    } = _ref2;\n    return utils.compileLocalizedPathname({\n      ...utils.normalizeNameOrNameWithParams(href),\n      locale,\n      pathnames: config$1.pathnames\n    });\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createLocalizedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar createSharedNavigationFns = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\n\nfunction createNavigation(routing) {\n  function useTypedLocale() {\n    return useLocale.default();\n  }\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns.default(useTypedLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname() {\n    const pathname = useBasePathname.default(config);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? utils.getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter() {\n    const router = navigation.useRouter();\n    const curLocale = useTypedLocale();\n    const nextPathname = navigation.usePathname();\n    return React.useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n\n          // @ts-expect-error -- We're passing a domain here just in case\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale,\n            domain: window.location.host\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createSharedPathnamesNavigation(routing) {\n  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);\n  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);\n  function Link(props, ref) {\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      localeCookie: localeCookie,\n      localePrefix: localePrefix\n    }, props));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(pathname) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(pathname) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function usePathname() {\n    const result = useBasePathname.default({\n      localePrefix,\n      defaultLocale: routing === null || routing === void 0 ? void 0 : routing.defaultLocale\n    });\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return result;\n  }\n  function useRouter() {\n    return useBaseRouter.default(localePrefix, localeCookie);\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter\n  };\n}\n\nexports[\"default\"] = createSharedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELGdDQUFnQyxtQkFBTyxDQUFDLDBJQUE2QztBQUNyRixZQUFZLG1CQUFPLENBQUMsaUdBQU87QUFDM0IsYUFBYSxtQkFBTyxDQUFDLGtHQUF5QjtBQUM5QyxpQkFBaUIsbUJBQU8sQ0FBQyw4R0FBaUI7QUFDMUMsZ0JBQWdCLG1CQUFPLENBQUMsNEdBQWdCO0FBQ3hDLHNCQUFzQixtQkFBTyxDQUFDLHdIQUFzQjtBQUNwRCxvQkFBb0IsbUJBQU8sQ0FBQyxvSEFBb0I7O0FBRWhELCtCQUErQixpQ0FBaUM7O0FBRWhFOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyRkFBMkYsYUFBYTtBQUN4RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSwrRkFBK0YsZUFBZTtBQUM5RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGRldmVsb3BtZW50XFxuYXZpZ2F0aW9uXFxyZWFjdC1jbGllbnRcXGNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycyA9IHJlcXVpcmUoJy4uLy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnKTtcbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG52YXIgY29uZmlnID0gcmVxdWlyZSgnLi4vLi4vcm91dGluZy9jb25maWcuanMnKTtcbnZhciBDbGllbnRMaW5rID0gcmVxdWlyZSgnLi9DbGllbnRMaW5rLmpzJyk7XG52YXIgcmVkaXJlY3RzID0gcmVxdWlyZSgnLi9yZWRpcmVjdHMuanMnKTtcbnZhciB1c2VCYXNlUGF0aG5hbWUgPSByZXF1aXJlKCcuL3VzZUJhc2VQYXRobmFtZS5qcycpO1xudmFyIHVzZUJhc2VSb3V0ZXIgPSByZXF1aXJlKCcuL3VzZUJhc2VSb3V0ZXIuanMnKTtcblxuZnVuY3Rpb24gX2ludGVyb3BEZWZhdWx0IChlKSB7IHJldHVybiBlICYmIGUuX19lc01vZHVsZSA/IGUgOiB7IGRlZmF1bHQ6IGUgfTsgfVxuXG52YXIgUmVhY3RfX2RlZmF1bHQgPSAvKiNfX1BVUkVfXyovX2ludGVyb3BEZWZhdWx0KFJlYWN0KTtcblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBDb25zaWRlciBzd2l0Y2hpbmcgdG8gYGNyZWF0ZU5hdmlnYXRpb25gIChzZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2Jsb2cvbmV4dC1pbnRsLTMtMjIjY3JlYXRlLW5hdmlnYXRpb24pXG4gKiovXG5mdW5jdGlvbiBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uKHJvdXRpbmcpIHtcbiAgY29uc3QgbG9jYWxlUHJlZml4ID0gY29uZmlnLnJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcocm91dGluZyA9PT0gbnVsbCB8fCByb3V0aW5nID09PSB2b2lkIDAgPyB2b2lkIDAgOiByb3V0aW5nLmxvY2FsZVByZWZpeCk7XG4gIGNvbnN0IGxvY2FsZUNvb2tpZSA9IGNvbmZpZy5yZWNlaXZlTG9jYWxlQ29va2llKHJvdXRpbmcgPT09IG51bGwgfHwgcm91dGluZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcm91dGluZy5sb2NhbGVDb29raWUpO1xuICBmdW5jdGlvbiBMaW5rKHByb3BzLCByZWYpIHtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0X19kZWZhdWx0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChDbGllbnRMaW5rLmRlZmF1bHQsIF9yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuZXh0ZW5kcyh7XG4gICAgICByZWY6IHJlZixcbiAgICAgIGxvY2FsZUNvb2tpZTogbG9jYWxlQ29va2llLFxuICAgICAgbG9jYWxlUHJlZml4OiBsb2NhbGVQcmVmaXhcbiAgICB9LCBwcm9wcykpO1xuICB9XG4gIGNvbnN0IExpbmtXaXRoUmVmID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoTGluayk7XG4gIExpbmtXaXRoUmVmLmRpc3BsYXlOYW1lID0gJ0xpbmsnO1xuICBmdW5jdGlvbiByZWRpcmVjdChwYXRobmFtZSkge1xuICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cbiAgICByZXR1cm4gcmVkaXJlY3RzLmNsaWVudFJlZGlyZWN0KHtcbiAgICAgIHBhdGhuYW1lLFxuICAgICAgbG9jYWxlUHJlZml4XG4gICAgfSwgLi4uYXJncyk7XG4gIH1cbiAgZnVuY3Rpb24gcGVybWFuZW50UmVkaXJlY3QocGF0aG5hbWUpIHtcbiAgICBmb3IgKHZhciBfbGVuMiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbjIgPiAxID8gX2xlbjIgLSAxIDogMCksIF9rZXkyID0gMTsgX2tleTIgPCBfbGVuMjsgX2tleTIrKykge1xuICAgICAgYXJnc1tfa2V5MiAtIDFdID0gYXJndW1lbnRzW19rZXkyXTtcbiAgICB9XG4gICAgcmV0dXJuIHJlZGlyZWN0cy5jbGllbnRQZXJtYW5lbnRSZWRpcmVjdCh7XG4gICAgICBwYXRobmFtZSxcbiAgICAgIGxvY2FsZVByZWZpeFxuICAgIH0sIC4uLmFyZ3MpO1xuICB9XG4gIGZ1bmN0aW9uIHVzZVBhdGhuYW1lKCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IHVzZUJhc2VQYXRobmFtZS5kZWZhdWx0KHtcbiAgICAgIGxvY2FsZVByZWZpeCxcbiAgICAgIGRlZmF1bHRMb2NhbGU6IHJvdXRpbmcgPT09IG51bGwgfHwgcm91dGluZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcm91dGluZy5kZWZhdWx0TG9jYWxlXG4gICAgfSk7XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBNaXJyb3IgdGhlIGJlaGF2aW9yIGZyb20gTmV4dC5qcywgd2hlcmUgYG51bGxgIGlzIHJldHVybmVkIHdoZW4gYHVzZVBhdGhuYW1lYCBpcyB1c2VkIG91dHNpZGUgb2YgTmV4dCwgYnV0IHRoZSB0eXBlcyBpbmRpY2F0ZSB0aGF0IGEgc3RyaW5nIGlzIGFsd2F5cyByZXR1cm5lZC5cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIGZ1bmN0aW9uIHVzZVJvdXRlcigpIHtcbiAgICByZXR1cm4gdXNlQmFzZVJvdXRlci5kZWZhdWx0KGxvY2FsZVByZWZpeCwgbG9jYWxlQ29va2llKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIExpbms6IExpbmtXaXRoUmVmLFxuICAgIHJlZGlyZWN0LFxuICAgIHBlcm1hbmVudFJlZGlyZWN0LFxuICAgIHVzZVBhdGhuYW1lLFxuICAgIHVzZVJvdXRlclxuICB9O1xufVxuXG5leHBvcnRzLmRlZmF1bHQgPSBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/redirects.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar redirects = __webpack_require__(/*! ../shared/redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function clientRedirect(params) {\n    let locale;\n    try {\n      // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n      locale = useLocale.default();\n    } catch (e) {\n      {\n        throw new Error('`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.');\n      }\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn({\n      ...params,\n      locale\n    }, ...args);\n  };\n}\nconst clientRedirect = createRedirectFn(redirects.baseRedirect);\nconst clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);\n\nexports.clientPermanentRedirect = clientPermanentRedirect;\nexports.clientRedirect = clientRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = navigation.usePathname();\n  const locale = useLocale.default();\n  return React.useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = utils.getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = utils.unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = utils.getLocaleAsPrefix(locale);\n      if (utils.hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = utils.unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexports[\"default\"] = useBasePathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * Returns a wrapped instance of `useRouter` from `next/navigation` that\n * will automatically localize the `href` parameters it receives.\n *\n * @example\n * ```tsx\n * 'use client';\n *\n * import {useRouter} from 'next-intl/client';\n *\n * const router = useRouter();\n *\n * // When the user is on `/en`, the router will navigate to `/en/about`\n * router.push('/about');\n *\n * // Optionally, you can switch the locale by passing the second argument\n * router.push('/about', {locale: 'de'});\n * ```\n */ function useBaseRouter(localePrefix, localeCookie) {\n    const router = navigation.useRouter();\n    const locale = useLocale.default();\n    const pathname = navigation.usePathname();\n    return React.useMemo({\n        \"useBaseRouter.useMemo\": ()=>{\n            function localize(href, nextLocale) {\n                let curPathname = window.location.pathname;\n                const basePath = utils.getBasePath(pathname);\n                if (basePath) curPathname = curPathname.replace(basePath, '');\n                const targetLocale = nextLocale || locale;\n                // We generate a prefix in any case, but decide\n                // in `localizeHref` if we apply it or not\n                const prefix = utils$1.getLocalePrefix(targetLocale, localePrefix);\n                return utils$1.localizeHref(href, targetLocale, locale, curPathname, prefix);\n            }\n            function createHandler(fn) {\n                return function handler(href, options) {\n                    const { locale: nextLocale, ...rest } = options || {};\n                    syncLocaleCookie.default(localeCookie, pathname, locale, nextLocale);\n                    const args = [\n                        localize(href, nextLocale)\n                    ];\n                    if (Object.keys(rest).length > 0) {\n                        args.push(rest);\n                    }\n                    // @ts-expect-error -- This is ok\n                    return fn(...args);\n                };\n            }\n            return {\n                ...router,\n                push: createHandler(router.push),\n                replace: createHandler(router.replace),\n                prefetch: createHandler(router.prefetch)\n            };\n        }\n    }[\"useBaseRouter.useMemo\"], [\n        locale,\n        localeCookie,\n        localePrefix,\n        pathname,\n        router\n    ]);\n}\nexports[\"default\"] = useBaseRouter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar NextLink = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar NextLink__default = /*#__PURE__*/ _interopDefault(NextLink);\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction BaseLink(_ref, ref) {\n    let { defaultLocale, href, locale, localeCookie, onClick, prefetch, unprefixed, ...rest } = _ref;\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    const linkLocale = locale || curLocale;\n    const host = useHost();\n    const finalHref = // Only after hydration (to avoid mismatches)\n    host && // If there is an `unprefixed` prop, the\n    // `defaultLocale` might differ by domain\n    unprefixed && // Unprefix the pathname if a domain matches\n    (unprefixed.domains[host] === linkLocale || // … and handle unknown domains by applying the\n    // global `defaultLocale` (e.g. on localhost)\n    !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    function onLinkClick(event) {\n        syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== 'production') {\n            console.error('The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`');\n        }\n        prefetch = false;\n    }\n    return /*#__PURE__*/ React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: finalHref,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch\n    }, rest));\n}\nfunction useHost() {\n    const [host, setHost] = React.useState();\n    React.useEffect({\n        \"useHost.useEffect\": ()=>{\n            setHost(window.location.host);\n        }\n    }[\"useHost.useEffect\"], []);\n    return host;\n}\nvar BaseLink$1 = /*#__PURE__*/ React.forwardRef(BaseLink);\nexports[\"default\"] = BaseLink$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvQmFzZUxpbmsuanMiLCJtYXBwaW5ncyI6InFEQUNhO0FBRWJBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELElBQUlDLDRCQUE0QkMsbUJBQU9BLENBQUMsMElBQTZDO0FBQ3JGLElBQUlDLFdBQVdELG1CQUFPQSxDQUFDLHdFQUFXO0FBQ2xDLElBQUlFLGFBQWFGLG1CQUFPQSxDQUFDLHlFQUFpQjtBQUMxQyxJQUFJRyxRQUFRSCxtQkFBT0EsQ0FBQyxpR0FBTztBQUMzQixJQUFJSSxZQUFZSixtQkFBT0EsQ0FBQyxrSEFBaUM7QUFDekQsSUFBSUssbUJBQW1CTCxtQkFBT0EsQ0FBQyxvSEFBdUI7QUFFdEQsU0FBU00sZ0JBQWlCQyxDQUFDO0lBQUksT0FBT0EsS0FBS0EsRUFBRUMsVUFBVSxHQUFHRCxJQUFJO1FBQUVFLFNBQVNGO0lBQUU7QUFBRztBQUU5RSxJQUFJRyxvQkFBb0IsV0FBVyxHQUFFSixnQkFBZ0JMO0FBQ3JELElBQUlVLGlCQUFpQixXQUFXLEdBQUVMLGdCQUFnQkg7QUFFbEQsU0FBU1MsU0FBU0MsSUFBSSxFQUFFQyxHQUFHO0lBQ3pCLElBQUksRUFDRkMsYUFBYSxFQUNiQyxJQUFJLEVBQ0pDLE1BQU0sRUFDTkMsWUFBWSxFQUNaQyxPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsVUFBVSxFQUNWLEdBQUdDLE1BQ0osR0FBR1Q7SUFDSixNQUFNVSxZQUFZbkIsVUFBVUssT0FBTztJQUNuQyxNQUFNZSxtQkFBbUJQLFVBQVUsUUFBUUEsV0FBV007SUFDdEQsTUFBTUUsYUFBYVIsVUFBVU07SUFDN0IsTUFBTUcsT0FBT0M7SUFDYixNQUFNQyxZQUNOLDZDQUE2QztJQUM3Q0YsUUFDQSx3Q0FBd0M7SUFDeEMseUNBQXlDO0lBQ3pDTCxjQUNBLDRDQUE0QztJQUM1Q0EsQ0FBQUEsV0FBV1EsT0FBTyxDQUFDSCxLQUFLLEtBQUtELGNBQzdCLCtDQUErQztJQUMvQyw2Q0FBNkM7SUFDN0MsQ0FBQzlCLE9BQU9tQyxJQUFJLENBQUNULFdBQVdRLE9BQU8sRUFBRUUsUUFBUSxDQUFDTCxTQUFTSCxjQUFjUixpQkFBaUIsQ0FBQ0UsTUFBSyxJQUFLSSxXQUFXVyxRQUFRLEdBQUdoQjtJQUVuSCw2REFBNkQ7SUFDN0QsNERBQTREO0lBQzVELE1BQU1nQixXQUFXOUIsV0FBVytCLFdBQVc7SUFDdkMsU0FBU0MsWUFBWUMsS0FBSztRQUN4QjlCLGlCQUFpQkksT0FBTyxDQUFDUyxjQUFjYyxVQUFVVCxXQUFXTjtRQUM1RCxJQUFJRSxTQUFTQSxRQUFRZ0I7SUFDdkI7SUFDQSxJQUFJWCxrQkFBa0I7UUFDcEIsSUFBSUosWUFBWSxrQkFBa0IsY0FBYztZQUM5Q2dCLFFBQVFDLEtBQUssQ0FBQztRQUNoQjtRQUNBakIsV0FBVztJQUNiO0lBQ0EsT0FBTyxXQUFXLEdBQUVULGVBQWVGLE9BQU8sQ0FBQzZCLGFBQWEsQ0FBQzVCLGtCQUFrQkQsT0FBTyxFQUFFViwwQkFBMEJ3QyxPQUFPLENBQUM7UUFDcEh6QixLQUFLQTtRQUNMRSxNQUFNWTtRQUNOWSxVQUFVaEIsbUJBQW1CUCxTQUFTd0I7UUFDdEN0QixTQUFTZTtRQUNUZCxVQUFVQTtJQUNaLEdBQUdFO0FBQ0w7QUFDQSxTQUFTSztJQUNQLE1BQU0sQ0FBQ0QsTUFBTWdCLFFBQVEsR0FBR3ZDLE1BQU13QyxRQUFRO0lBQ3RDeEMsTUFBTXlDLFNBQVM7NkJBQUM7WUFDZEYsUUFBUUcsT0FBT0MsUUFBUSxDQUFDcEIsSUFBSTtRQUM5Qjs0QkFBRyxFQUFFO0lBQ0wsT0FBT0E7QUFDVDtBQUNBLElBQUlxQixhQUFhLFdBQVcsR0FBRTVDLE1BQU02QyxVQUFVLENBQUNwQztBQUUvQ2Ysa0JBQWUsR0FBR2tEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXG5hdmlnYXRpb25cXHNoYXJlZFxcQmFzZUxpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG4ndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzID0gcmVxdWlyZSgnLi4vLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcycpO1xudmFyIE5leHRMaW5rID0gcmVxdWlyZSgnbmV4dC9saW5rJyk7XG52YXIgbmF2aWdhdGlvbiA9IHJlcXVpcmUoJ25leHQvbmF2aWdhdGlvbicpO1xudmFyIFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcbnZhciB1c2VMb2NhbGUgPSByZXF1aXJlKCcuLi8uLi9yZWFjdC1jbGllbnQvdXNlTG9jYWxlLmpzJyk7XG52YXIgc3luY0xvY2FsZUNvb2tpZSA9IHJlcXVpcmUoJy4vc3luY0xvY2FsZUNvb2tpZS5qcycpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcERlZmF1bHQgKGUpIHsgcmV0dXJuIGUgJiYgZS5fX2VzTW9kdWxlID8gZSA6IHsgZGVmYXVsdDogZSB9OyB9XG5cbnZhciBOZXh0TGlua19fZGVmYXVsdCA9IC8qI19fUFVSRV9fKi9faW50ZXJvcERlZmF1bHQoTmV4dExpbmspO1xudmFyIFJlYWN0X19kZWZhdWx0ID0gLyojX19QVVJFX18qL19pbnRlcm9wRGVmYXVsdChSZWFjdCk7XG5cbmZ1bmN0aW9uIEJhc2VMaW5rKF9yZWYsIHJlZikge1xuICBsZXQge1xuICAgIGRlZmF1bHRMb2NhbGUsXG4gICAgaHJlZixcbiAgICBsb2NhbGUsXG4gICAgbG9jYWxlQ29va2llLFxuICAgIG9uQ2xpY2ssXG4gICAgcHJlZmV0Y2gsXG4gICAgdW5wcmVmaXhlZCxcbiAgICAuLi5yZXN0XG4gIH0gPSBfcmVmO1xuICBjb25zdCBjdXJMb2NhbGUgPSB1c2VMb2NhbGUuZGVmYXVsdCgpO1xuICBjb25zdCBpc0NoYW5naW5nTG9jYWxlID0gbG9jYWxlICE9IG51bGwgJiYgbG9jYWxlICE9PSBjdXJMb2NhbGU7XG4gIGNvbnN0IGxpbmtMb2NhbGUgPSBsb2NhbGUgfHwgY3VyTG9jYWxlO1xuICBjb25zdCBob3N0ID0gdXNlSG9zdCgpO1xuICBjb25zdCBmaW5hbEhyZWYgPVxuICAvLyBPbmx5IGFmdGVyIGh5ZHJhdGlvbiAodG8gYXZvaWQgbWlzbWF0Y2hlcylcbiAgaG9zdCAmJlxuICAvLyBJZiB0aGVyZSBpcyBhbiBgdW5wcmVmaXhlZGAgcHJvcCwgdGhlXG4gIC8vIGBkZWZhdWx0TG9jYWxlYCBtaWdodCBkaWZmZXIgYnkgZG9tYWluXG4gIHVucHJlZml4ZWQgJiYgKFxuICAvLyBVbnByZWZpeCB0aGUgcGF0aG5hbWUgaWYgYSBkb21haW4gbWF0Y2hlc1xuICB1bnByZWZpeGVkLmRvbWFpbnNbaG9zdF0gPT09IGxpbmtMb2NhbGUgfHxcbiAgLy8g4oCmIGFuZCBoYW5kbGUgdW5rbm93biBkb21haW5zIGJ5IGFwcGx5aW5nIHRoZVxuICAvLyBnbG9iYWwgYGRlZmF1bHRMb2NhbGVgIChlLmcuIG9uIGxvY2FsaG9zdClcbiAgIU9iamVjdC5rZXlzKHVucHJlZml4ZWQuZG9tYWlucykuaW5jbHVkZXMoaG9zdCkgJiYgY3VyTG9jYWxlID09PSBkZWZhdWx0TG9jYWxlICYmICFsb2NhbGUpID8gdW5wcmVmaXhlZC5wYXRobmFtZSA6IGhyZWY7XG5cbiAgLy8gVGhlIHR5cGVzIGFyZW4ndCBlbnRpcmVseSBjb3JyZWN0IGhlcmUuIE91dHNpZGUgb2YgTmV4dC5qc1xuICAvLyBgdXNlUGFyYW1zYCBjYW4gYmUgY2FsbGVkLCBidXQgdGhlIHJldHVybiB0eXBlIGlzIGBudWxsYC5cbiAgY29uc3QgcGF0aG5hbWUgPSBuYXZpZ2F0aW9uLnVzZVBhdGhuYW1lKCk7XG4gIGZ1bmN0aW9uIG9uTGlua0NsaWNrKGV2ZW50KSB7XG4gICAgc3luY0xvY2FsZUNvb2tpZS5kZWZhdWx0KGxvY2FsZUNvb2tpZSwgcGF0aG5hbWUsIGN1ckxvY2FsZSwgbG9jYWxlKTtcbiAgICBpZiAob25DbGljaykgb25DbGljayhldmVudCk7XG4gIH1cbiAgaWYgKGlzQ2hhbmdpbmdMb2NhbGUpIHtcbiAgICBpZiAocHJlZmV0Y2ggJiYgXCJkZXZlbG9wbWVudFwiICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1RoZSBgcHJlZmV0Y2hgIHByb3AgaXMgY3VycmVudGx5IG5vdCBzdXBwb3J0ZWQgd2hlbiB1c2luZyB0aGUgYGxvY2FsZWAgcHJvcCBvbiBgTGlua2AgdG8gc3dpdGNoIHRoZSBsb2NhbGUuYCcpO1xuICAgIH1cbiAgICBwcmVmZXRjaCA9IGZhbHNlO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3RfX2RlZmF1bHQuZGVmYXVsdC5jcmVhdGVFbGVtZW50KE5leHRMaW5rX19kZWZhdWx0LmRlZmF1bHQsIF9yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuZXh0ZW5kcyh7XG4gICAgcmVmOiByZWYsXG4gICAgaHJlZjogZmluYWxIcmVmLFxuICAgIGhyZWZMYW5nOiBpc0NoYW5naW5nTG9jYWxlID8gbG9jYWxlIDogdW5kZWZpbmVkLFxuICAgIG9uQ2xpY2s6IG9uTGlua0NsaWNrLFxuICAgIHByZWZldGNoOiBwcmVmZXRjaFxuICB9LCByZXN0KSk7XG59XG5mdW5jdGlvbiB1c2VIb3N0KCkge1xuICBjb25zdCBbaG9zdCwgc2V0SG9zdF0gPSBSZWFjdC51c2VTdGF0ZSgpO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldEhvc3Qod2luZG93LmxvY2F0aW9uLmhvc3QpO1xuICB9LCBbXSk7XG4gIHJldHVybiBob3N0O1xufVxudmFyIEJhc2VMaW5rJDEgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihCYXNlTGluayk7XG5cbmV4cG9ydHMuZGVmYXVsdCA9IEJhc2VMaW5rJDE7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzIiwicmVxdWlyZSIsIk5leHRMaW5rIiwibmF2aWdhdGlvbiIsIlJlYWN0IiwidXNlTG9jYWxlIiwic3luY0xvY2FsZUNvb2tpZSIsIl9pbnRlcm9wRGVmYXVsdCIsImUiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsIk5leHRMaW5rX19kZWZhdWx0IiwiUmVhY3RfX2RlZmF1bHQiLCJCYXNlTGluayIsIl9yZWYiLCJyZWYiLCJkZWZhdWx0TG9jYWxlIiwiaHJlZiIsImxvY2FsZSIsImxvY2FsZUNvb2tpZSIsIm9uQ2xpY2siLCJwcmVmZXRjaCIsInVucHJlZml4ZWQiLCJyZXN0IiwiY3VyTG9jYWxlIiwiaXNDaGFuZ2luZ0xvY2FsZSIsImxpbmtMb2NhbGUiLCJob3N0IiwidXNlSG9zdCIsImZpbmFsSHJlZiIsImRvbWFpbnMiLCJrZXlzIiwiaW5jbHVkZXMiLCJwYXRobmFtZSIsInVzZVBhdGhuYW1lIiwib25MaW5rQ2xpY2siLCJldmVudCIsImNvbnNvbGUiLCJlcnJvciIsImNyZWF0ZUVsZW1lbnQiLCJleHRlbmRzIiwiaHJlZkxhbmciLCJ1bmRlZmluZWQiLCJzZXRIb3N0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIkJhc2VMaW5rJDEiLCJmb3J3YXJkUmVmIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction LegacyBaseLink(_ref, ref) {\n    let { href, locale, localeCookie, localePrefixMode, prefix, ...rest } = _ref;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const [localizedHref, setLocalizedHref] = React.useState({\n        \"LegacyBaseLink.useState\": ()=>utils.isLocalizableHref(href) && (localePrefixMode !== 'never' || isChangingLocale) ? // For the `localePrefix: 'as-needed' strategy, the href shouldn't\n            // be prefixed if the locale is the default locale. To determine this, we\n            // need a) the default locale and b) the information if we use prefixed\n            // routing. The default locale can vary by domain, therefore during the\n            // RSC as well as the SSR render, we can't determine the default locale\n            // statically. Therefore we always prefix the href since this will\n            // always result in a valid URL, even if it might cause a redirect. This\n            // is better than pointing to a non-localized href during the server\n            // render, which would potentially be wrong. The final href is\n            // determined in the effect below.\n            utils.prefixHref(href, prefix) : href\n    }[\"LegacyBaseLink.useState\"]);\n    React.useEffect({\n        \"LegacyBaseLink.useEffect\": ()=>{\n            if (!pathname) return;\n            setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));\n        }\n    }[\"LegacyBaseLink.useEffect\"], [\n        curLocale,\n        href,\n        locale,\n        pathname,\n        prefix\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: localizedHref,\n        locale: locale,\n        localeCookie: localeCookie\n    }, rest));\n}\nconst LegacyBaseLinkWithRef = /*#__PURE__*/ React.forwardRef(LegacyBaseLink);\nLegacyBaseLinkWithRef.displayName = 'ClientLink';\nexports[\"default\"] = LegacyBaseLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvTGVnYWN5QmFzZUxpbmsuanMiLCJtYXBwaW5ncyI6InFEQUNhO0FBRWJBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELElBQUlDLDRCQUE0QkMsbUJBQU9BLENBQUMsMElBQTZDO0FBQ3JGLElBQUlDLGFBQWFELG1CQUFPQSxDQUFDLHlFQUFpQjtBQUMxQyxJQUFJRSxRQUFRRixtQkFBT0EsQ0FBQyxpR0FBTztBQUMzQixJQUFJRyxZQUFZSCxtQkFBT0EsQ0FBQyxrSEFBaUM7QUFDekQsSUFBSUksUUFBUUosbUJBQU9BLENBQUMsOEZBQXVCO0FBQzNDLElBQUlLLFdBQVdMLG1CQUFPQSxDQUFDLG9HQUFlO0FBRXRDLFNBQVNNLGdCQUFpQkMsQ0FBQztJQUFJLE9BQU9BLEtBQUtBLEVBQUVDLFVBQVUsR0FBR0QsSUFBSTtRQUFFRSxTQUFTRjtJQUFFO0FBQUc7QUFFOUUsSUFBSUcsaUJBQWlCLFdBQVcsR0FBRUosZ0JBQWdCSjtBQUVsRCxTQUFTUyxlQUFlQyxJQUFJLEVBQUVDLEdBQUc7SUFDL0IsSUFBSSxFQUNGQyxJQUFJLEVBQ0pDLE1BQU0sRUFDTkMsWUFBWSxFQUNaQyxnQkFBZ0IsRUFDaEJDLE1BQU0sRUFDTixHQUFHQyxNQUNKLEdBQUdQO0lBQ0osNkRBQTZEO0lBQzdELDREQUE0RDtJQUM1RCxNQUFNUSxXQUFXbkIsV0FBV29CLFdBQVc7SUFDdkMsTUFBTUMsWUFBWW5CLFVBQVVNLE9BQU87SUFDbkMsTUFBTWMsbUJBQW1CUixXQUFXTztJQUNwQyxNQUFNLENBQUNFLGVBQWVDLGlCQUFpQixHQUFHdkIsTUFBTXdCLFFBQVE7bUNBQUMsSUFBTXRCLE1BQU11QixpQkFBaUIsQ0FBQ2IsU0FBVUcsQ0FBQUEscUJBQXFCLFdBQVdNLGdCQUFlLElBQ2hKLGtFQUFrRTtZQUNsRSx5RUFBeUU7WUFDekUsdUVBQXVFO1lBQ3ZFLHVFQUF1RTtZQUN2RSx1RUFBdUU7WUFDdkUsa0VBQWtFO1lBQ2xFLHdFQUF3RTtZQUN4RSxvRUFBb0U7WUFDcEUsOERBQThEO1lBQzlELGtDQUFrQztZQUNsQ25CLE1BQU13QixVQUFVLENBQUNkLE1BQU1JLFVBQVVKOztJQUNqQ1osTUFBTTJCLFNBQVM7b0NBQUM7WUFDZCxJQUFJLENBQUNULFVBQVU7WUFDZkssaUJBQWlCckIsTUFBTTBCLFlBQVksQ0FBQ2hCLE1BQU1DLFFBQVFPLFdBQVdGLFVBQVVGO1FBQ3pFO21DQUFHO1FBQUNJO1FBQVdSO1FBQU1DO1FBQVFLO1FBQVVGO0tBQU87SUFDOUMsT0FBTyxXQUFXLEdBQUVSLGVBQWVELE9BQU8sQ0FBQ3NCLGFBQWEsQ0FBQzFCLFNBQVNJLE9BQU8sRUFBRVYsMEJBQTBCaUMsT0FBTyxDQUFDO1FBQzNHbkIsS0FBS0E7UUFDTEMsTUFBTVU7UUFDTlQsUUFBUUE7UUFDUkMsY0FBY0E7SUFDaEIsR0FBR0c7QUFDTDtBQUNBLE1BQU1jLHdCQUF3QixXQUFXLEdBQUUvQixNQUFNZ0MsVUFBVSxDQUFDdkI7QUFDNURzQixzQkFBc0JFLFdBQVcsR0FBRztBQUVwQ3RDLGtCQUFlLEdBQUdvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGRldmVsb3BtZW50XFxuYXZpZ2F0aW9uXFxzaGFyZWRcXExlZ2FjeUJhc2VMaW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycyA9IHJlcXVpcmUoJy4uLy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnKTtcbnZhciBuYXZpZ2F0aW9uID0gcmVxdWlyZSgnbmV4dC9uYXZpZ2F0aW9uJyk7XG52YXIgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xudmFyIHVzZUxvY2FsZSA9IHJlcXVpcmUoJy4uLy4uL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMnKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoJy4uLy4uL3NoYXJlZC91dGlscy5qcycpO1xudmFyIEJhc2VMaW5rID0gcmVxdWlyZSgnLi9CYXNlTGluay5qcycpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcERlZmF1bHQgKGUpIHsgcmV0dXJuIGUgJiYgZS5fX2VzTW9kdWxlID8gZSA6IHsgZGVmYXVsdDogZSB9OyB9XG5cbnZhciBSZWFjdF9fZGVmYXVsdCA9IC8qI19fUFVSRV9fKi9faW50ZXJvcERlZmF1bHQoUmVhY3QpO1xuXG5mdW5jdGlvbiBMZWdhY3lCYXNlTGluayhfcmVmLCByZWYpIHtcbiAgbGV0IHtcbiAgICBocmVmLFxuICAgIGxvY2FsZSxcbiAgICBsb2NhbGVDb29raWUsXG4gICAgbG9jYWxlUHJlZml4TW9kZSxcbiAgICBwcmVmaXgsXG4gICAgLi4ucmVzdFxuICB9ID0gX3JlZjtcbiAgLy8gVGhlIHR5cGVzIGFyZW4ndCBlbnRpcmVseSBjb3JyZWN0IGhlcmUuIE91dHNpZGUgb2YgTmV4dC5qc1xuICAvLyBgdXNlUGFyYW1zYCBjYW4gYmUgY2FsbGVkLCBidXQgdGhlIHJldHVybiB0eXBlIGlzIGBudWxsYC5cbiAgY29uc3QgcGF0aG5hbWUgPSBuYXZpZ2F0aW9uLnVzZVBhdGhuYW1lKCk7XG4gIGNvbnN0IGN1ckxvY2FsZSA9IHVzZUxvY2FsZS5kZWZhdWx0KCk7XG4gIGNvbnN0IGlzQ2hhbmdpbmdMb2NhbGUgPSBsb2NhbGUgIT09IGN1ckxvY2FsZTtcbiAgY29uc3QgW2xvY2FsaXplZEhyZWYsIHNldExvY2FsaXplZEhyZWZdID0gUmVhY3QudXNlU3RhdGUoKCkgPT4gdXRpbHMuaXNMb2NhbGl6YWJsZUhyZWYoaHJlZikgJiYgKGxvY2FsZVByZWZpeE1vZGUgIT09ICduZXZlcicgfHwgaXNDaGFuZ2luZ0xvY2FsZSkgP1xuICAvLyBGb3IgdGhlIGBsb2NhbGVQcmVmaXg6ICdhcy1uZWVkZWQnIHN0cmF0ZWd5LCB0aGUgaHJlZiBzaG91bGRuJ3RcbiAgLy8gYmUgcHJlZml4ZWQgaWYgdGhlIGxvY2FsZSBpcyB0aGUgZGVmYXVsdCBsb2NhbGUuIFRvIGRldGVybWluZSB0aGlzLCB3ZVxuICAvLyBuZWVkIGEpIHRoZSBkZWZhdWx0IGxvY2FsZSBhbmQgYikgdGhlIGluZm9ybWF0aW9uIGlmIHdlIHVzZSBwcmVmaXhlZFxuICAvLyByb3V0aW5nLiBUaGUgZGVmYXVsdCBsb2NhbGUgY2FuIHZhcnkgYnkgZG9tYWluLCB0aGVyZWZvcmUgZHVyaW5nIHRoZVxuICAvLyBSU0MgYXMgd2VsbCBhcyB0aGUgU1NSIHJlbmRlciwgd2UgY2FuJ3QgZGV0ZXJtaW5lIHRoZSBkZWZhdWx0IGxvY2FsZVxuICAvLyBzdGF0aWNhbGx5LiBUaGVyZWZvcmUgd2UgYWx3YXlzIHByZWZpeCB0aGUgaHJlZiBzaW5jZSB0aGlzIHdpbGxcbiAgLy8gYWx3YXlzIHJlc3VsdCBpbiBhIHZhbGlkIFVSTCwgZXZlbiBpZiBpdCBtaWdodCBjYXVzZSBhIHJlZGlyZWN0LiBUaGlzXG4gIC8vIGlzIGJldHRlciB0aGFuIHBvaW50aW5nIHRvIGEgbm9uLWxvY2FsaXplZCBocmVmIGR1cmluZyB0aGUgc2VydmVyXG4gIC8vIHJlbmRlciwgd2hpY2ggd291bGQgcG90ZW50aWFsbHkgYmUgd3JvbmcuIFRoZSBmaW5hbCBocmVmIGlzXG4gIC8vIGRldGVybWluZWQgaW4gdGhlIGVmZmVjdCBiZWxvdy5cbiAgdXRpbHMucHJlZml4SHJlZihocmVmLCBwcmVmaXgpIDogaHJlZik7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFwYXRobmFtZSkgcmV0dXJuO1xuICAgIHNldExvY2FsaXplZEhyZWYodXRpbHMubG9jYWxpemVIcmVmKGhyZWYsIGxvY2FsZSwgY3VyTG9jYWxlLCBwYXRobmFtZSwgcHJlZml4KSk7XG4gIH0sIFtjdXJMb2NhbGUsIGhyZWYsIGxvY2FsZSwgcGF0aG5hbWUsIHByZWZpeF0pO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0X19kZWZhdWx0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChCYXNlTGluay5kZWZhdWx0LCBfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmV4dGVuZHMoe1xuICAgIHJlZjogcmVmLFxuICAgIGhyZWY6IGxvY2FsaXplZEhyZWYsXG4gICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgbG9jYWxlQ29va2llOiBsb2NhbGVDb29raWVcbiAgfSwgcmVzdCkpO1xufVxuY29uc3QgTGVnYWN5QmFzZUxpbmtXaXRoUmVmID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoTGVnYWN5QmFzZUxpbmspO1xuTGVnYWN5QmFzZUxpbmtXaXRoUmVmLmRpc3BsYXlOYW1lID0gJ0NsaWVudExpbmsnO1xuXG5leHBvcnRzLmRlZmF1bHQgPSBMZWdhY3lCYXNlTGlua1dpdGhSZWY7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzIiwicmVxdWlyZSIsIm5hdmlnYXRpb24iLCJSZWFjdCIsInVzZUxvY2FsZSIsInV0aWxzIiwiQmFzZUxpbmsiLCJfaW50ZXJvcERlZmF1bHQiLCJlIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJSZWFjdF9fZGVmYXVsdCIsIkxlZ2FjeUJhc2VMaW5rIiwiX3JlZiIsInJlZiIsImhyZWYiLCJsb2NhbGUiLCJsb2NhbGVDb29raWUiLCJsb2NhbGVQcmVmaXhNb2RlIiwicHJlZml4IiwicmVzdCIsInBhdGhuYW1lIiwidXNlUGF0aG5hbWUiLCJjdXJMb2NhbGUiLCJpc0NoYW5naW5nTG9jYWxlIiwibG9jYWxpemVkSHJlZiIsInNldExvY2FsaXplZEhyZWYiLCJ1c2VTdGF0ZSIsImlzTG9jYWxpemFibGVIcmVmIiwicHJlZml4SHJlZiIsInVzZUVmZmVjdCIsImxvY2FsaXplSHJlZiIsImNyZWF0ZUVsZW1lbnQiLCJleHRlbmRzIiwiTGVnYWN5QmFzZUxpbmtXaXRoUmVmIiwiZm9yd2FyZFJlZiIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config$1 = config.receiveRoutingConfig(routing || {});\n  {\n    utils.validateReceivedConfig(config$1);\n  }\n  const pathnames = config$1.pathnames;\n\n  // This combination requires that the current host is known in order to\n  // compute a correct pathname. Since that can only be achieved by reading from\n  // headers, this would break static rendering. Therefore, as a workaround we\n  // always add a prefix in this case to be on the safe side. The downside is\n  // that the user might get redirected again if the middleware detects that the\n  // prefix is not needed.\n  const forcePrefixSsr = config$1.localePrefix.mode === 'as-needed' && config$1.domains || undefined;\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    let pathname, params, query;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      query = href.query;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = utils$1.isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = utils$1.isPromise(localePromiseOrValue) ? React.use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname(\n    // @ts-expect-error -- This is ok\n    {\n      locale: locale || curLocale,\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || forcePrefixSsr || undefined) : pathname;\n    return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref\n      // @ts-expect-error -- Available after the validation\n      ,\n      defaultLocale: config$1.defaultLocale\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config$1.localeCookie\n      // Provide the minimal relevant information to the client side in order\n      // to potentially remove the prefix in case of the `forcePrefixSsr` case\n      ,\n      unprefixed: forcePrefixSsr && isLocalizable ? {\n        domains: config$1.domains.reduce((acc, domain) => {\n          // @ts-expect-error -- This is ok\n          acc[domain.domain] = domain.defaultLocale;\n          return acc;\n        }, {}),\n        pathname: getPathname(\n        // @ts-expect-error -- This is ok\n        {\n          locale: curLocale,\n          href: pathnames == null ? {\n            pathname,\n            query\n          } : {\n            pathname,\n            query,\n            params\n          }\n        }, false)\n      } : undefined\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += utils.serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = utils.compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...utils.normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config$1.pathnames\n      });\n    }\n    return utils.applyPathnamePrefix(pathname, locale, config$1,\n    // @ts-expect-error -- This is ok\n    args.domain, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      return fn(\n      // @ts-expect-error -- We're forcing the prefix when no domain is provided\n      getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);\n    };\n  }\n  const redirect = getRedirectFn(navigation.redirect);\n  const permanentRedirect = getRedirectFn(navigation.permanentRedirect);\n  return {\n    config: config$1,\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\nexports[\"default\"] = createSharedNavigationFns;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/redirects.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function baseRedirect(params) {\n    const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);\n\n    // This logic is considered legacy and is replaced by `applyPathnamePrefix`.\n    // We keep it this way for now for backwards compatibility.\n    const localizedPathname = params.localePrefix.mode === 'never' || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn(localizedPathname, ...args);\n  };\n}\nconst baseRedirect = createRedirectFn(navigation.redirect);\nconst basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);\n\nexports.basePermanentRedirect = basePermanentRedirect;\nexports.baseRedirect = baseRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = utils.getBasePath(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = \"\".concat(name, \"=\").concat(nextLocale, \";\");\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += \"\".concat(targetKey);\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\nexports[\"default\"] = syncLocaleCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname(_ref) {\n  let {\n    pathname,\n    locale,\n    params,\n    pathnames,\n    query\n  } = _ref;\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath) {\n    const template = typeof namedPath === 'string' ? namedPath : namedPath[locale];\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(_ref2 => {\n        let [key, value] = _ref2;\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = \"(\\\\[)?\\\\[...\".concat(key, \"\\\\](\\\\])?\");\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = \"\\\\[\".concat(key, \"\\\\]\");\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = utils.normalizeTrailingSlash(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(template, \"\\nParams: \").concat(JSON.stringify(params)));\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath);\n    return compiled;\n  } else {\n    const {\n      pathname: href,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(href);\n    const compiled = compilePath(namedPath);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (utils.matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname) {\n  let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, domain, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (utils.isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      let defaultLocale = routing.defaultLocale;\n      if (routing.domains) {\n        const domainConfig = routing.domains.find(cur => cur.domain === domain);\n        if (domainConfig) {\n          defaultLocale = domainConfig.defaultLocale;\n        } else {\n          if (!domain) {\n            console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n          }\n        }\n      }\n      shouldPrefix = defaultLocale !== locale;\n    }\n  }\n  return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  var _config$localePrefix;\n  if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexports.applyPathnamePrefix = applyPathnamePrefix;\nexports.compileLocalizedPathname = compileLocalizedPathname;\nexports.getBasePath = getBasePath;\nexports.getRoute = getRoute;\nexports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;\nexports.serializeSearchParams = serializeSearchParams;\nexports.validateReceivedConfig = validateReceivedConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return function () {\n    try {\n      return hook(...arguments);\n    } catch (_unused) {\n      throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context\") );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useIntl.useTranslations);\nconst useFormatter = callHook('useFormatter', useIntl.useFormatter);\n\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function (k) {\n  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n    enumerable: true,\n    get: function () { return useIntl[k]; }\n  });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\n\nlet hasWarnedForParams = false;\nfunction useLocale() {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n  const params = navigation.useParams();\n  let locale;\n  try {\n    // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n    locale = _useLocale.useLocale();\n  } catch (error) {\n    if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === 'string') {\n      if (!hasWarnedForParams) {\n        console.warn('Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.');\n        hasWarnedForParams = true;\n      }\n      locale = params[constants.LOCALE_SEGMENT_NAME];\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\n\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing.js":
/*!************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar defineRouting = __webpack_require__(/*! ./routing/defineRouting.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js\");\n\n\n\nexports.defineRouting = defineRouting.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELG9CQUFvQixtQkFBTyxDQUFDLDRHQUE0Qjs7OztBQUl4RCxxQkFBcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxkZXZlbG9wbWVudFxccm91dGluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBkZWZpbmVSb3V0aW5nID0gcmVxdWlyZSgnLi9yb3V0aW5nL2RlZmluZVJvdXRpbmcuanMnKTtcblxuXG5cbmV4cG9ydHMuZGVmaW5lUm91dGluZyA9IGRlZmluZVJvdXRpbmcuZGVmYXVsdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/config.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/config.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction receiveRoutingConfig(input) {\n  var _input$localeDetectio, _input$alternateLinks;\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,\n    alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {\n    name: 'NEXT_LOCALE',\n    maxAge: ********,\n    // 1 year\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexports.receiveLocaleCookie = receiveLocaleCookie;\nexports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;\nexports.receiveRoutingConfig = receiveRoutingConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/defineRouting.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction defineRouting(config) {\n  return config;\n}\n\nexports[\"default\"] = defineRouting;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0Q7QUFDQTtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGRlZmluZVJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG5mdW5jdGlvbiBkZWZpbmVSb3V0aW5nKGNvbmZpZykge1xuICByZXR1cm4gY29uZmlnO1xufVxuXG5leHBvcnRzLmRlZmF1bHQgPSBkZWZpbmVSb3V0aW5nO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error('Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale');\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = 'locale';\n\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSwwQkFBMEI7QUFDMUIsMkJBQTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXHNoYXJlZFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuLy8gU2hvdWxkIHRha2UgcHJlY2VkZW5jZSBvdmVyIHRoZSBjb29raWVcbmNvbnN0IEhFQURFUl9MT0NBTEVfTkFNRSA9ICdYLU5FWFQtSU5UTC1MT0NBTEUnO1xuXG4vLyBJbiBhIFVSTCBsaWtlIFwiL2VuLVVTL2Fib3V0XCIsIHRoZSBsb2NhbGUgc2VnbWVudCBpcyBcImVuLVVTXCJcbmNvbnN0IExPQ0FMRV9TRUdNRU5UX05BTUUgPSAnbG9jYWxlJztcblxuZXhwb3J0cy5IRUFERVJfTE9DQUxFX05BTUUgPSBIRUFERVJfTE9DQUxFX05BTUU7XG5leHBvcnRzLkxPQ0FMRV9TRUdNRU5UX05BTUUgPSBMT0NBTEVfU0VHTUVOVF9OQU1FO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction localizeHref(href, locale) {\n  let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;\n  let curPathname = arguments.length > 3 ? arguments[3] : undefined;\n  let prefix = arguments.length > 4 ? arguments[4] : undefined;\n  if (!isLocalizableHref(href)) {\n    return href;\n  }\n  const isSwitchingLocale = locale !== curLocale;\n  const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);\n  const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;\n  if (shouldPrefix && prefix != null) {\n    return prefixHref(href, prefix);\n  }\n  return href;\n}\nfunction prefixHref(href, prefix) {\n  let prefixedHref;\n  if (typeof href === 'string') {\n    prefixedHref = prefixPathname(prefix, href);\n  } else {\n    prefixedHref = {\n      ...href\n    };\n    if (href.pathname) {\n      prefixedHref.pathname = prefixPathname(prefix, href.pathname);\n    }\n  }\n  return prefixedHref;\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(\"^\".concat(prefix)), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(\"\".concat(prefix, \"/\"));\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  var _localePrefix$prefixe;\n  return localePrefix.mode !== 'never' && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(\"^\".concat(regexPattern, \"$\"));\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexports.getLocaleAsPrefix = getLocaleAsPrefix;\nexports.getLocalePrefix = getLocalePrefix;\nexports.getSortedPathnames = getSortedPathnames;\nexports.hasPathnamePrefixed = hasPathnamePrefixed;\nexports.isLocalizableHref = isLocalizableHref;\nexports.isPromise = isPromise;\nexports.localizeHref = localizeHref;\nexports.matchesPathname = matchesPathname;\nexports.normalizeTrailingSlash = normalizeTrailingSlash;\nexports.prefixHref = prefixHref;\nexports.prefixPathname = prefixPathname;\nexports.templateToRegex = templateToRegex;\nexports.unprefixPathname = unprefixPathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXF92aXJ0dWFsXFxfcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction s(n, s) {\n    let { defaultLocale: p, href: f, locale: u, localeCookie: m, onClick: h, prefetch: d, unprefixed: k, ...x } = n;\n    const L = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), g = null != u && u !== L, j = u || L, v = function() {\n        const [e, o] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n        return (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            o(window.location.host);\n        }, []), e;\n    }(), w = v && k && (k.domains[v] === j || !Object.keys(k.domains).includes(v) && L === p && !u) ? k.pathname : f, C = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    return g && (d && console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\"), d = !1), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((next_link__WEBPACK_IMPORTED_MODULE_0___default()), (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"extends\"])({\n        ref: s,\n        href: w,\n        hrefLang: g ? u : void 0,\n        onClick: function(e) {\n            (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(m, C, L, u), h && h(e);\n        },\n        prefetch: d\n    }, x));\n}\nvar p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(s);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction f(l, f) {\n    let { href: p, locale: u, localeCookie: d, localePrefixMode: x, prefix: j, ...k } = l;\n    const h = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)(), v = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(), C = u !== v, [L, g] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isLocalizableHref)(p) && (\"never\" !== x || C) ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.prefixHref)(p, j) : p);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        h && g((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.localizeHref)(p, u, v, h, j));\n    }, [\n        v,\n        p,\n        u,\n        h,\n        j\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__[\"extends\"])({\n        ref: f,\n        href: L,\n        locale: u,\n        localeCookie: d\n    }, k));\n}\nconst p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(f);\np.displayName = \"ClientLink\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction o(o,e,n,a){if(!o||!(a!==n&&null!=a)||!e)return;const c=(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(e),f=\"\"!==c?c:\"/\",{name:r,...i}=o;i.path||(i.path=f);let l=\"\".concat(r,\"=\").concat(a,\";\");for(const[t,o]of Object.entries(i)){l+=\"\".concat(\"maxAge\"===t?\"max-age\":t),\"boolean\"!=typeof o&&(l+=\"=\"+o),l+=\";\"}document.cookie=l}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vc2hhcmVkL3N5bmNMb2NhbGVDb29raWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMsb0JBQW9CLG9DQUFvQyxRQUFRLHNEQUFDLG9CQUFvQixZQUFZLEdBQUcsbUJBQW1CLGtDQUFrQyxHQUFHLG9DQUFvQyw0RUFBNEUsRUFBRSxrQkFBdUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXG5hdmlnYXRpb25cXHNoYXJlZFxcc3luY0xvY2FsZUNvb2tpZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Z2V0QmFzZVBhdGggYXMgdH1mcm9tXCIuL3V0aWxzLmpzXCI7ZnVuY3Rpb24gbyhvLGUsbixhKXtpZighb3x8IShhIT09biYmbnVsbCE9YSl8fCFlKXJldHVybjtjb25zdCBjPXQoZSksZj1cIlwiIT09Yz9jOlwiL1wiLHtuYW1lOnIsLi4uaX09bztpLnBhdGh8fChpLnBhdGg9Zik7bGV0IGw9XCJcIi5jb25jYXQocixcIj1cIikuY29uY2F0KGEsXCI7XCIpO2Zvcihjb25zdFt0LG9db2YgT2JqZWN0LmVudHJpZXMoaSkpe2wrPVwiXCIuY29uY2F0KFwibWF4QWdlXCI9PT10P1wibWF4LWFnZVwiOnQpLFwiYm9vbGVhblwiIT10eXBlb2YgbyYmKGwrPVwiPVwiK28pLGwrPVwiO1wifWRvY3VtZW50LmNvb2tpZT1sfWV4cG9ydHtvIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ f),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ s),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function f(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function f(e){let n=o[e];return n||(n=e),n}function s(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return s(f(n))}{const{pathname:e,...t}=n;return{...t,pathname:s(f(e))}}}function s(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:f}=a.localePrefix;let s;if(void 0!==c)s=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===f)s=!0;else if(\"as-needed\"===f){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\")}s=e!==n}return s?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-client/useLocale.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\");\nlet o=!1;function r(){const r=(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();let a;try{a=(0,use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__.useLocale)()}catch(e){if(\"string\"!=typeof(null==r?void 0:r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]))throw e;o||(console.warn(\"Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.\"),o=!0),a=r[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]}return a}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5SixTQUFTLGFBQWEsUUFBUSwwREFBQyxHQUFHLE1BQU0sSUFBSSxFQUFFLDZEQUFDLEdBQUcsU0FBUyxxQ0FBcUMscUVBQUMsV0FBVyw2VkFBNlYscUVBQUMsRUFBRSxTQUE4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxccmVhY3QtY2xpZW50XFx1c2VMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VzZVBhcmFtcyBhcyBlfWZyb21cIm5leHQvbmF2aWdhdGlvblwiO2ltcG9ydHt1c2VMb2NhbGUgYXMgdH1mcm9tXCJ1c2UtaW50bC9fdXNlTG9jYWxlXCI7aW1wb3J0e0xPQ0FMRV9TRUdNRU5UX05BTUUgYXMgbn1mcm9tXCIuLi9zaGFyZWQvY29uc3RhbnRzLmpzXCI7bGV0IG89ITE7ZnVuY3Rpb24gcigpe2NvbnN0IHI9ZSgpO2xldCBhO3RyeXthPXQoKX1jYXRjaChlKXtpZihcInN0cmluZ1wiIT10eXBlb2YobnVsbD09cj92b2lkIDA6cltuXSkpdGhyb3cgZTtvfHwoY29uc29sZS53YXJuKFwiRGVwcmVjYXRpb24gd2FybmluZzogYHVzZUxvY2FsZWAgaGFzIHJldHVybmVkIGEgZGVmYXVsdCBmcm9tIGB1c2VQYXJhbXMoKS5sb2NhbGVgIHNpbmNlIG5vIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCBhbmNlc3RvciB3YXMgZm91bmQgZm9yIHRoZSBjYWxsaW5nIGNvbXBvbmVudC4gVGhpcyBiZWhhdmlvciB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi4gUGxlYXNlIGVuc3VyZSBhbGwgQ2xpZW50IENvbXBvbmVudHMgdGhhdCB1c2UgYG5leHQtaW50bGAgYXJlIHdyYXBwZWQgaW4gYSBgTmV4dEludGxDbGllbnRQcm92aWRlcmAuXCIpLG89ITApLGE9cltuXX1yZXR1cm4gYX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUErSixxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcc2hhcmVkXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0e2V4dGVuZHMgYXMgZX1mcm9tXCIuLi9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzXCI7aW1wb3J0IGwgZnJvbVwicmVhY3RcIjtpbXBvcnR7SW50bFByb3ZpZGVyIGFzIHR9ZnJvbVwidXNlLWludGwvX0ludGxQcm92aWRlclwiO2Z1bmN0aW9uIHIocil7bGV0e2xvY2FsZTpvLC4uLml9PXI7aWYoIW8pdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGRldGVybWluZSBsb2NhbGUgaW4gYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgLCBwbGVhc2UgcHJvdmlkZSB0aGUgYGxvY2FsZWAgcHJvcCBleHBsaWNpdGx5LlxcblxcblNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI2xvY2FsZVwiKTtyZXR1cm4gbC5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm99LGkpKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJleHRlbmRzIiwiZSIsImwiLCJJbnRsUHJvdmlkZXIiLCJ0IiwiciIsImxvY2FsZSIsIm8iLCJpIiwiRXJyb3IiLCJjcmVhdGVFbGVtZW50IiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXHNoYXJlZFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(c,o);return(f||l)&&null!=c?e(t,c):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function o(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function c(n,t){const e=o(n),r=o(t);return s(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return\"/\"+n}function s(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function a(n){return n.includes(\"[[...\")}function p(n){return n.includes(\"[...\")}function h(n){return n.includes(\"[\")}function g(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}function v(n){return\"function\"==typeof n.then}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ })

};
;