/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/scientific/page";
exports.ids = ["app/[locale]/scientific/page"];
exports.modules = {

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../i18n/routing */ \"(rsc)/./i18n/routing.ts\");\n/* harmony import */ var _components_ClientProviders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ClientProviders */ \"(rsc)/./components/ClientProviders.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./components/Navigation.tsx\");\n\n\n\n\n\n\n\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    // Validate that the incoming `locale` parameter is valid\n    if (!_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // Providing all messages to the client\n    // side is the easiest way to get started\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // Set the direction based on locale\n    const direction = locale === 'ar' ? 'rtl' : 'ltr';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: direction,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientProviders__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/scientific/page.tsx":
/*!******************************************!*\
  !*** ./app/[locale]/scientific/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScientificCalculatorPage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\");\n/* harmony import */ var _components_pages_ScientificCalculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/pages/ScientificCalculator */ \"(rsc)/./components/pages/ScientificCalculator.tsx\");\n\n\n\nasync function generateMetadata({ params }) {\n    const { locale } = await params;\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        locale,\n        namespace: 'scientific'\n    });\n    return {\n        title: t('title'),\n        description: t('seo.intro'),\n        openGraph: {\n            title: t('title'),\n            description: t('seo.intro'),\n            locale: locale\n        }\n    };\n}\nfunction ScientificCalculatorPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_ScientificCalculator__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\scientific\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vc2NpZW50aWZpYy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW1EO0FBRXdCO0FBTXBFLGVBQWVFLGlCQUFpQixFQUFFQyxNQUFNLEVBQVM7SUFDdEQsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNRDtJQUN6QixNQUFNRSxJQUFJLE1BQU1MLDREQUFlQSxDQUFDO1FBQUVJO1FBQVFFLFdBQVc7SUFBYTtJQUVsRSxPQUFPO1FBQ0xDLE9BQU9GLEVBQUU7UUFDVEcsYUFBYUgsRUFBRTtRQUNmSSxXQUFXO1lBQ1RGLE9BQU9GLEVBQUU7WUFDVEcsYUFBYUgsRUFBRTtZQUNmRCxRQUFRQTtRQUNWO0lBQ0Y7QUFDRjtBQUVlLFNBQVNNO0lBQ3RCLHFCQUFPLDhEQUFDVCw4RUFBb0JBOzs7OztBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcYXBwXFxbbG9jYWxlXVxcc2NpZW50aWZpY1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0VHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcic7XG5pbXBvcnQgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IFNjaWVudGlmaWNDYWxjdWxhdG9yIGZyb20gJ0AvY29tcG9uZW50cy9wYWdlcy9TY2llbnRpZmljQ2FsY3VsYXRvcic7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9O1xufTtcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlTWV0YWRhdGEoeyBwYXJhbXMgfTogUHJvcHMpOiBQcm9taXNlPE1ldGFkYXRhPiB7XG4gIGNvbnN0IHsgbG9jYWxlIH0gPSBhd2FpdCBwYXJhbXM7XG4gIGNvbnN0IHQgPSBhd2FpdCBnZXRUcmFuc2xhdGlvbnMoeyBsb2NhbGUsIG5hbWVzcGFjZTogJ3NjaWVudGlmaWMnIH0pO1xuXG4gIHJldHVybiB7XG4gICAgdGl0bGU6IHQoJ3RpdGxlJyksXG4gICAgZGVzY3JpcHRpb246IHQoJ3Nlby5pbnRybycpLFxuICAgIG9wZW5HcmFwaDoge1xuICAgICAgdGl0bGU6IHQoJ3RpdGxlJyksXG4gICAgICBkZXNjcmlwdGlvbjogdCgnc2VvLmludHJvJyksXG4gICAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICB9LFxuICB9O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTY2llbnRpZmljQ2FsY3VsYXRvclBhZ2UoKSB7XG4gIHJldHVybiA8U2NpZW50aWZpY0NhbGN1bGF0b3IgLz47XG59XG4iXSwibmFtZXMiOlsiZ2V0VHJhbnNsYXRpb25zIiwiU2NpZW50aWZpY0NhbGN1bGF0b3IiLCJnZW5lcmF0ZU1ldGFkYXRhIiwicGFyYW1zIiwibG9jYWxlIiwidCIsIm5hbWVzcGFjZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJvcGVuR3JhcGgiLCJTY2llbnRpZmljQ2FsY3VsYXRvclBhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/scientific/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f9654bee10ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSFBcXERlc2t0b3BcXEFwcHNcXGNhbGN1bGF0b3JcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmOTY1NGJlZTEwY2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'PowerCalc - Professional Calculator Suite',\n    description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',\n    keywords: 'calculator, scientific calculator, financial calculator, unit converter, health calculator, BMI calculator',\n    authors: [\n        {\n            name: 'PowerCalc Team'\n        }\n    ],\n    creator: 'PowerCalc',\n    publisher: 'PowerCalc',\n    robots: 'index, follow',\n    openGraph: {\n        type: 'website',\n        locale: 'en_US',\n        url: 'https://powercalc.com',\n        title: 'PowerCalc - Professional Calculator Suite',\n        description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',\n        siteName: 'PowerCalc'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'PowerCalc - Professional Calculator Suite',\n        description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ClientProviders.tsx":
/*!****************************************!*\
  !*** ./components/ClientProviders.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\components\\ClientProviders.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\components\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Apps\\calculator\\components\\pages\\ScientificCalculator.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./i18n/request.ts":
/*!*************************!*\
  !*** ./i18n/request.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./i18n/routing.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuL3JlcXVlc3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ2tEO0FBQ2hCO0FBRWxDLGlFQUFlQSw0REFBZ0JBLENBQUMsT0FBTyxFQUFDRSxhQUFhLEVBQUM7SUFDcEQsdURBQXVEO0lBQ3ZELElBQUlDLFNBQVMsTUFBTUQ7SUFFbkIscUNBQXFDO0lBQ3JDLElBQUksQ0FBQ0MsVUFBVSxDQUFDRiw2Q0FBT0EsQ0FBQ0csT0FBTyxDQUFDQyxRQUFRLENBQUNGLFNBQWdCO1FBQ3ZEQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNoQztJQUVBLE9BQU87UUFDTEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBYSxFQUFFSixPQUFPLE1BQU0sR0FBR0ssT0FBTztJQUNoRTtBQUNGLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxIUFxcRGVza3RvcFxcQXBwc1xcY2FsY3VsYXRvclxcaTE4blxccmVxdWVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge25vdEZvdW5kfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcbmltcG9ydCB7cm91dGluZ30gZnJvbSAnLi9yb3V0aW5nJztcblxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoe3JlcXVlc3RMb2NhbGV9KSA9PiB7XG4gIC8vIFRoaXMgdHlwaWNhbGx5IGNvcnJlc3BvbmRzIHRvIHRoZSBgW2xvY2FsZV1gIHNlZ21lbnRcbiAgbGV0IGxvY2FsZSA9IGF3YWl0IHJlcXVlc3RMb2NhbGU7XG5cbiAgLy8gRW5zdXJlIHRoYXQgYSB2YWxpZCBsb2NhbGUgaXMgdXNlZFxuICBpZiAoIWxvY2FsZSB8fCAhcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSBhcyBhbnkpKSB7XG4gICAgbG9jYWxlID0gcm91dGluZy5kZWZhdWx0TG9jYWxlO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBsb2NhbGUsXG4gICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcbiAgfTtcbn0pO1xuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJyb3V0aW5nIiwicmVxdWVzdExvY2FsZSIsImxvY2FsZSIsImxvY2FsZXMiLCJpbmNsdWRlcyIsImRlZmF1bHRMb2NhbGUiLCJtZXNzYWdlcyIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./i18n/routing.ts":
/*!*************************!*\
  !*** ./i18n/routing.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   localeNames: () => (/* binding */ localeNames),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   routing: () => (/* binding */ routing),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\");\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // A list of all locales that are supported\n    locales: [\n        'en',\n        'fr',\n        'es',\n        'ar'\n    ],\n    // Used when no locale matches\n    defaultLocale: 'en',\n    // The pathnames configuration\n    pathnames: {\n        '/': '/',\n        '/scientific': {\n            en: '/scientific',\n            fr: '/scientifique',\n            es: '/cientifica',\n            ar: '/علمية'\n        },\n        '/financial': {\n            en: '/financial',\n            fr: '/financiere',\n            es: '/financiera',\n            ar: '/مالية'\n        },\n        '/conversion': {\n            en: '/conversion',\n            fr: '/conversion',\n            es: '/conversion',\n            ar: '/تحويل'\n        },\n        '/health': {\n            en: '/health',\n            fr: '/sante',\n            es: '/salud',\n            ar: '/صحة'\n        }\n    }\n});\nconst locales = routing.locales;\nconst defaultLocale = routing.defaultLocale;\nconst localeNames = {\n    en: 'English',\n    fr: 'Français',\n    es: 'Español',\n    ar: 'العربية'\n};\nconst pathnames = routing.pathnames;\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nconst { Link, redirect, usePathname, useRouter } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(routing);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./i18n/routing.ts\n");

/***/ }),

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fscientific%2Fpage&page=%2F%5Blocale%5D%2Fscientific%2Fpage&appPaths=%2F%5Blocale%5D%2Fscientific%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fscientific%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fscientific%2Fpage&page=%2F%5Blocale%5D%2Fscientific%2Fpage&appPaths=%2F%5Blocale%5D%2Fscientific%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fscientific%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/scientific/page.tsx */ \"(rsc)/./app/[locale]/scientific/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'scientific',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\scientific\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\app\\\\[locale]\\\\scientific\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/scientific/page\",\n        pathname: \"/[locale]/scientific\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fscientific%2Fpage&page=%2F%5Blocale%5D%2Fscientific%2Fpage&appPaths=%2F%5Blocale%5D%2Fscientific%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fscientific%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ClientProviders.tsx */ \"(rsc)/./components/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navigation.tsx */ \"(rsc)/./components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/pages/ScientificCalculator.tsx */ \"(rsc)/./components/pages/ScientificCalculator.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/ClientProviders.tsx":
/*!****************************************!*\
  !*** ./components/ClientProviders.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toaster */ \"(ssr)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(ssr)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(ssr)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Create a client component for providers that need client-side functionality\nfunction ClientProviders({ children }) {\n    const queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClient();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"system\",\n            enableSystem: true,\n            disableTransitionOnChange: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__.Toaster, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ClientProviders.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ClientProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../i18n/routing */ \"(ssr)/./i18n/routing.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst Navigation = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('nav');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const toggleMenu = ()=>setIsOpen(!isOpen);\n    const changeLocale = (newLocale)=>{\n        // Get the current path without the locale\n        const pathWithoutLocale = pathname?.replace(`/${locale}`, '') || '/';\n        // Navigate to the same path with the new locale\n        router.push(`/${newLocale}${pathWithoutLocale}`);\n    };\n    const getLocalizedPath = (path)=>{\n        return `/${locale}${path}`;\n    };\n    const navItems = [\n        {\n            href: '/',\n            label: t('home')\n        },\n        {\n            href: '/scientific',\n            label: t('scientific')\n        },\n        {\n            href: '/financial',\n            label: t('financial')\n        },\n        {\n            href: '/conversion',\n            label: t('conversion')\n        },\n        {\n            href: '/health',\n            label: t('health')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white dark:bg-gray-900 shadow-lg border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: getLocalizedPath('/'),\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"PowerCalc\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: getLocalizedPath(item.href),\n                                        className: \"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors\",\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: _i18n_routing__WEBPACK_IMPORTED_MODULE_6__.localeNames[locale]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: _i18n_routing__WEBPACK_IMPORTED_MODULE_6__.routing.locales.map((loc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                    onClick: ()=>changeLocale(loc),\n                                                    className: locale === loc ? 'bg-blue-50 dark:bg-blue-900' : '',\n                                                    children: _i18n_routing__WEBPACK_IMPORTED_MODULE_6__.localeNames[loc]\n                                                }, loc, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: toggleMenu,\n                                className: \"text-gray-700 dark:text-gray-300\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 25\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 53\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-gray-900 border-t\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: getLocalizedPath(item.href),\n                                    className: \"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: t('language')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: _i18n_routing__WEBPACK_IMPORTED_MODULE_6__.routing.locales.map((loc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    changeLocale(loc);\n                                                    setIsOpen(false);\n                                                },\n                                                className: `block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${locale === loc ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400'}`,\n                                                children: _i18n_routing__WEBPACK_IMPORTED_MODULE_6__.localeNames[loc]\n                                            }, loc, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\Navigation.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst ScientificCalculator = ()=>{\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    // Keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScientificCalculator.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ScientificCalculator.useEffect.handleKeyPress\": (event)=>{\n                    const { key } = event;\n                    if (key >= '0' && key <= '9') {\n                        inputNumber(key);\n                    } else if (key === '.') {\n                        inputNumber('.');\n                    } else if (key === '+') {\n                        inputOperation('+');\n                    } else if (key === '-') {\n                        inputOperation('-');\n                    } else if (key === '*') {\n                        inputOperation('×');\n                    } else if (key === '/') {\n                        event.preventDefault();\n                        inputOperation('÷');\n                    } else if (key === 'Enter' || key === '=') {\n                        performCalculation();\n                    } else if (key === 'Escape' || key === 'c' || key === 'C') {\n                        clear();\n                    } else if (key === 'Backspace') {\n                        setDisplay(display.slice(0, -1) || '0');\n                    }\n                }\n            }[\"ScientificCalculator.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ScientificCalculator.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ScientificCalculator.useEffect\"];\n        }\n    }[\"ScientificCalculator.useEffect\"], [\n        display\n    ]);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = previousValue || '0';\n                const newValue = calculate(parseFloat(currentValue), inputValue, operation);\n                const historyEntry = `${currentValue} ${operation} ${inputValue} = ${newValue}`;\n                setHistory({\n                    \"ScientificCalculator.useCallback[inputOperation]\": (prev)=>[\n                            historyEntry,\n                            ...prev.slice(0, 19)\n                        ]\n                }[\"ScientificCalculator.useCallback[inputOperation]\"]);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            case 'mod':\n                return firstValue % secondValue;\n            case 'xroot':\n                return Math.pow(secondValue, 1 / firstValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performUnaryOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performUnaryOperation]\": (op)=>{\n            const currentValue = parseFloat(display);\n            let result;\n            const angleMultiplier = angleMode === 'deg' ? Math.PI / 180 : 1;\n            switch(op){\n                case '√':\n                    result = Math.sqrt(currentValue);\n                    break;\n                case '∛':\n                    result = Math.cbrt(currentValue);\n                    break;\n                case 'x²':\n                    result = Math.pow(currentValue, 2);\n                    break;\n                case 'x³':\n                    result = Math.pow(currentValue, 3);\n                    break;\n                case '1/x':\n                    result = currentValue !== 0 ? 1 / currentValue : NaN;\n                    break;\n                case 'sin':\n                    result = Math.sin(currentValue * angleMultiplier);\n                    break;\n                case 'cos':\n                    result = Math.cos(currentValue * angleMultiplier);\n                    break;\n                case 'tan':\n                    result = Math.tan(currentValue * angleMultiplier);\n                    break;\n                case 'asin':\n                    result = Math.asin(currentValue) / angleMultiplier;\n                    break;\n                case 'acos':\n                    result = Math.acos(currentValue) / angleMultiplier;\n                    break;\n                case 'atan':\n                    result = Math.atan(currentValue) / angleMultiplier;\n                    break;\n                case 'sinh':\n                    result = Math.sinh(currentValue);\n                    break;\n                case 'cosh':\n                    result = Math.cosh(currentValue);\n                    break;\n                case 'tanh':\n                    result = Math.tanh(currentValue);\n                    break;\n                case 'ln':\n                    result = Math.log(currentValue);\n                    break;\n                case 'log':\n                    result = Math.log10(currentValue);\n                    break;\n                case 'log2':\n                    result = Math.log2(currentValue);\n                    break;\n                case 'exp':\n                    result = Math.exp(currentValue);\n                    break;\n                case '10^x':\n                    result = Math.pow(10, currentValue);\n                    break;\n                case '2^x':\n                    result = Math.pow(2, currentValue);\n                    break;\n                case '!':\n                    result = factorial(currentValue);\n                    break;\n                case '+/-':\n                    result = -currentValue;\n                    break;\n                case 'abs':\n                    result = Math.abs(currentValue);\n                    break;\n                case 'floor':\n                    result = Math.floor(currentValue);\n                    break;\n                case 'ceil':\n                    result = Math.ceil(currentValue);\n                    break;\n                case 'round':\n                    result = Math.round(currentValue);\n                    break;\n                default:\n                    return;\n            }\n            const historyEntry = `${op}(${currentValue}) = ${result}`;\n            setHistory({\n                \"ScientificCalculator.useCallback[performUnaryOperation]\": (prev)=>[\n                        historyEntry,\n                        ...prev.slice(0, 19)\n                    ]\n            }[\"ScientificCalculator.useCallback[performUnaryOperation]\"]);\n            setDisplay(isNaN(result) ? t('error') || 'Error' : String(result));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[performUnaryOperation]\"], [\n        display,\n        angleMode,\n        t\n    ]);\n    const factorial = (n)=>{\n        if (n < 0 || !Number.isInteger(n) || n > 170) return NaN;\n        if (n === 0 || n === 1) return 1;\n        let result = 1;\n        for(let i = 2; i <= n; i++){\n            result *= i;\n        }\n        return result;\n    };\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'π':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                case 'φ':\n                    value = (1 + Math.sqrt(5)) / 2;\n                    break;\n                case '√2':\n                    value = Math.SQRT2;\n                    break;\n                case '√π':\n                    value = Math.sqrt(Math.PI);\n                    break;\n                case 'γ':\n                    value = 0.5772156649015329;\n                    break; // Euler-Mascheroni constant\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const performCalculation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performCalculation]\": ()=>{\n            const inputValue = parseFloat(display);\n            if (previousValue !== null && operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                const historyEntry = `${previousValue} ${operation} ${inputValue} = ${newValue}`;\n                setHistory({\n                    \"ScientificCalculator.useCallback[performCalculation]\": (prev)=>[\n                            historyEntry,\n                            ...prev.slice(0, 19)\n                        ]\n                }[\"ScientificCalculator.useCallback[performCalculation]\"]);\n                setDisplay(isNaN(newValue) ? t('error') || 'Error' : String(newValue));\n                setPreviousValue(null);\n                setOperation(null);\n                setWaitingForNewValue(true);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[performCalculation]\"], [\n        display,\n        previousValue,\n        operation,\n        t\n    ]);\n    const clearEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearEntry]\": ()=>{\n            setDisplay('0');\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clearEntry]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gray-50 py-8 ${isRTL ? 'font-arabic' : ''}`,\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    t('calculator')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: angleMode === 'deg' ? 'default' : 'secondary',\n                                                        children: t('angle_mode', {\n                                                            mode: angleMode === 'deg' ? t('degree') : t('radian')\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setAngleMode(angleMode === 'deg' ? 'rad' : 'deg'),\n                                                        children: angleMode === 'deg' ? 'RAD' : 'DEG'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: copyResult,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            defaultValue: \"basic\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                                    className: \"grid w-full grid-cols-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"basic\",\n                                                            children: t('basic')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"functions\",\n                                                            children: t('functions')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"constants\",\n                                                            children: t('constants')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"memory\",\n                                                            children: t('memory')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-3xl font-mono ${isRTL ? 'text-left' : 'text-right'} mb-2`,\n                                                                children: display\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            operation && previousValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-sm text-gray-400 ${isRTL ? 'text-left' : 'text-right'}`,\n                                                                children: [\n                                                                    previousValue,\n                                                                    \" \",\n                                                                    operation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t('angle_mode', {\n                                                                            mode: angleMode === 'deg' ? 'DEG' : 'RAD'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    memory !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"M: \",\n                                                                            memory\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"basic\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: clear,\n                                                                className: \"bg-red-50 hover:bg-red-100 text-red-700\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.slice(0, -1) || '0'),\n                                                                children: \"⌫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('^'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x^y\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('√'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"√\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('÷'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xf7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('7'),\n                                                                children: \"7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('8'),\n                                                                children: \"8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('9'),\n                                                                children: \"9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('x²'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x\\xb2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('×'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('4'),\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('5'),\n                                                                children: \"5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('6'),\n                                                                children: \"6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('1/x'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"1/x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('-'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('1'),\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('2'),\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('3'),\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('!'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"n!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('+'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('0'),\n                                                                className: \"col-span-2\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('.'),\n                                                                children: \".\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('+/-'),\n                                                                children: \"\\xb1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: performCalculation,\n                                                                className: \"bg-calculator-primary hover:bg-calculator-primary/90 text-white\",\n                                                                children: \"=\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"advanced\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('sin'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"sin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('cos'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"cos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('tan'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"tan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('ln'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"ln\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performUnaryOperation('log'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"log\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"functions\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-4 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryStore,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryRecall,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryAdd,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"M+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryClear,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"constants\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('π'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('e'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"e\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('φ'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"φ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('√2'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"√2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('√π'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"√π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('γ'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"γ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"memory\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-3 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: memoryStore,\n                                                                        className: \"bg-cyan-50 hover:bg-cyan-100 text-cyan-700\",\n                                                                        children: \"MS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: memoryRecall,\n                                                                        className: \"bg-cyan-50 hover:bg-cyan-100 text-cyan-700\",\n                                                                        children: \"MR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: memoryClear,\n                                                                        className: \"bg-cyan-50 hover:bg-cyan-100 text-cyan-700\",\n                                                                        children: \"MC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: memoryAdd,\n                                                                        className: \"bg-cyan-50 hover:bg-cyan-100 text-cyan-700\",\n                                                                        children: \"M+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: memorySubtract,\n                                                                        className: \"bg-cyan-50 hover:bg-cyan-100 text-cyan-700\",\n                                                                        children: \"M-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-gray-50 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Memory: \",\n                                                                        memory\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        t('history')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: clearHistory,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                                                children: history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 text-center py-4\",\n                                                    children: t('no_history')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 21\n                                                }, undefined) : history.map((calc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs bg-gray-50 p-2 rounded font-mono\",\n                                                        children: calc\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${isRTL ? 'text-right' : 'text-left'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: t('seo.features_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Advanced Mathematical Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Logarithmic & Exponential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature2')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Memory Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature3')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Calculation History\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature4')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Perfect For:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: t('seo.use_cases')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/pages/ScientificCalculator.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLDREQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUM5QyxHQUFHQyxLQUFLOzs7Ozs7QUFHYkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNLO1FBQ0NMLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCWiw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNPO1FBQ0NQLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjZCw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFmLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxjb21wb25lbnRzXFx1aVxcY2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicm91bmRlZC1sZyBib3JkZXIgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3ctc21cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuQ2FyZENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIkNhcmRDb250ZW50XCJcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGl0ZW1zLWNlbnRlciBwLTYgcHQtMFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkRm9vdGVyXCJcblxuZXhwb3J0IHsgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZEZvb3RlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 106,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 97,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 129,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 145,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 161,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* reexport safe */ sonner__WEBPACK_IMPORTED_MODULE_2__.toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tooltip.tsx":
/*!***********************************!*\
  !*** ./components/ui/tooltip.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\nconst Tooltip = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TooltipTrigger = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        sideOffset: sideOffset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./i18n/routing.ts":
/*!*************************!*\
  !*** ./i18n/routing.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   localeNames: () => (/* binding */ localeNames),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   routing: () => (/* binding */ routing),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(ssr)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\");\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // A list of all locales that are supported\n    locales: [\n        'en',\n        'fr',\n        'es',\n        'ar'\n    ],\n    // Used when no locale matches\n    defaultLocale: 'en',\n    // The pathnames configuration\n    pathnames: {\n        '/': '/',\n        '/scientific': {\n            en: '/scientific',\n            fr: '/scientifique',\n            es: '/cientifica',\n            ar: '/علمية'\n        },\n        '/financial': {\n            en: '/financial',\n            fr: '/financiere',\n            es: '/financiera',\n            ar: '/مالية'\n        },\n        '/conversion': {\n            en: '/conversion',\n            fr: '/conversion',\n            es: '/conversion',\n            ar: '/تحويل'\n        },\n        '/health': {\n            en: '/health',\n            fr: '/sante',\n            es: '/salud',\n            ar: '/صحة'\n        }\n    }\n});\nconst locales = routing.locales;\nconst defaultLocale = routing.defaultLocale;\nconst localeNames = {\n    en: 'English',\n    fr: 'Français',\n    es: 'Español',\n    ar: 'العربية'\n};\nconst pathnames = routing.pathnames;\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nconst { Link, redirect, usePathname, useRouter } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__.createNavigation)(routing);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./i18n/routing.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEhQXFxEZXNrdG9wXFxBcHBzXFxjYWxjdWxhdG9yXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ClientProviders.tsx */ \"(ssr)/./components/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navigation.tsx */ \"(ssr)/./components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/pages/ScientificCalculator.tsx */ \"(ssr)/./components/pages/ScientificCalculator.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Ccomponents%5C%5Cpages%5C%5CScientificCalculator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHP%5C%5CDesktop%5C%5CApps%5C%5Ccalculator%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/@formatjs","vendor-chunks/@floating-ui","vendor-chunks/next-intl","vendor-chunks/tailwind-merge","vendor-chunks/use-intl","vendor-chunks/@tanstack","vendor-chunks/sonner","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fscientific%2Fpage&page=%2F%5Blocale%5D%2Fscientific%2Fpage&appPaths=%2F%5Blocale%5D%2Fscientific%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fscientific%2Fpage.tsx&appDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHP%5CDesktop%5CApps%5Ccalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();