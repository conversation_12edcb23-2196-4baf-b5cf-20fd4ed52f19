"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/scientific/page",{

/***/ "(app-pages-browser)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ScientificCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    // Keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScientificCalculator.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ScientificCalculator.useEffect.handleKeyPress\": (event)=>{\n                    const { key } = event;\n                    if (key >= '0' && key <= '9') {\n                        inputNumber(key);\n                    } else if (key === '.') {\n                        inputNumber('.');\n                    } else if (key === '+') {\n                        inputOperation('+');\n                    } else if (key === '-') {\n                        inputOperation('-');\n                    } else if (key === '*') {\n                        inputOperation('×');\n                    } else if (key === '/') {\n                        event.preventDefault();\n                        inputOperation('÷');\n                    } else if (key === 'Enter' || key === '=') {\n                        performCalculation();\n                    } else if (key === 'Escape' || key === 'c' || key === 'C') {\n                        clear();\n                    } else if (key === 'Backspace') {\n                        setDisplay(display.slice(0, -1) || '0');\n                    }\n                }\n            }[\"ScientificCalculator.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ScientificCalculator.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ScientificCalculator.useEffect\"];\n        }\n    }[\"ScientificCalculator.useEffect\"], [\n        display\n    ]);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = previousValue || '0';\n                const newValue = calculate(parseFloat(currentValue), inputValue, operation);\n                const historyEntry = \"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue);\n                setHistory({\n                    \"ScientificCalculator.useCallback[inputOperation]\": (prev)=>[\n                            historyEntry,\n                            ...prev.slice(0, 19)\n                        ]\n                }[\"ScientificCalculator.useCallback[inputOperation]\"]);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            case 'mod':\n                return firstValue % secondValue;\n            case 'xroot':\n                return Math.pow(secondValue, 1 / firstValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performUnaryOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performUnaryOperation]\": (op)=>{\n            const currentValue = parseFloat(display);\n            let result;\n            const angleMultiplier = angleMode === 'deg' ? Math.PI / 180 : 1;\n            switch(op){\n                case '√':\n                    result = Math.sqrt(currentValue);\n                    break;\n                case '∛':\n                    result = Math.cbrt(currentValue);\n                    break;\n                case 'x²':\n                    result = Math.pow(currentValue, 2);\n                    break;\n                case 'x³':\n                    result = Math.pow(currentValue, 3);\n                    break;\n                case '1/x':\n                    result = currentValue !== 0 ? 1 / currentValue : NaN;\n                    break;\n                case 'sin':\n                    result = Math.sin(currentValue * angleMultiplier);\n                    break;\n                case 'cos':\n                    result = Math.cos(currentValue * angleMultiplier);\n                    break;\n                case 'tan':\n                    result = Math.tan(currentValue * angleMultiplier);\n                    break;\n                case 'asin':\n                    result = Math.asin(currentValue) / angleMultiplier;\n                    break;\n                case 'acos':\n                    result = Math.acos(currentValue) / angleMultiplier;\n                    break;\n                case 'atan':\n                    result = Math.atan(currentValue) / angleMultiplier;\n                    break;\n                case 'sinh':\n                    result = Math.sinh(currentValue);\n                    break;\n                case 'cosh':\n                    result = Math.cosh(currentValue);\n                    break;\n                case 'tanh':\n                    result = Math.tanh(currentValue);\n                    break;\n                case 'ln':\n                    result = Math.log(currentValue);\n                    break;\n                case 'log':\n                    result = Math.log10(currentValue);\n                    break;\n                case 'log2':\n                    result = Math.log2(currentValue);\n                    break;\n                case 'exp':\n                    result = Math.exp(currentValue);\n                    break;\n                case '10^x':\n                    result = Math.pow(10, currentValue);\n                    break;\n                case '2^x':\n                    result = Math.pow(2, currentValue);\n                    break;\n                case '!':\n                    result = factorial(currentValue);\n                    break;\n                case '+/-':\n                    result = -currentValue;\n                    break;\n                case 'abs':\n                    result = Math.abs(currentValue);\n                    break;\n                case 'floor':\n                    result = Math.floor(currentValue);\n                    break;\n                case 'ceil':\n                    result = Math.ceil(currentValue);\n                    break;\n                case 'round':\n                    result = Math.round(currentValue);\n                    break;\n                default:\n                    return;\n            }\n            const historyEntry = \"\".concat(op, \"(\").concat(currentValue, \") = \").concat(result);\n            setHistory({\n                \"ScientificCalculator.useCallback[performUnaryOperation]\": (prev)=>[\n                        historyEntry,\n                        ...prev.slice(0, 19)\n                    ]\n            }[\"ScientificCalculator.useCallback[performUnaryOperation]\"]);\n            setDisplay(isNaN(result) ? t('error') || 'Error' : String(result));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[performUnaryOperation]\"], [\n        display,\n        angleMode,\n        t\n    ]);\n    const factorial = (n)=>{\n        if (n < 0 || !Number.isInteger(n) || n > 170) return NaN;\n        if (n === 0 || n === 1) return 1;\n        let result = 1;\n        for(let i = 2; i <= n; i++){\n            result *= i;\n        }\n        return result;\n    };\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'pi':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    t('calculator')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: angleMode === 'deg' ? 'default' : 'secondary',\n                                                        children: t('angle_mode', {\n                                                            mode: angleMode === 'deg' ? t('degree') : t('radian')\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setAngleMode(angleMode === 'deg' ? 'rad' : 'deg'),\n                                                        children: angleMode === 'deg' ? 'RAD' : 'DEG'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: copyResult,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            defaultValue: \"basic\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                                    className: \"grid w-full grid-cols-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"basic\",\n                                                            children: t('basic')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"functions\",\n                                                            children: t('functions')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"constants\",\n                                                            children: t('constants')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-mono \".concat(isRTL ? 'text-left' : 'text-right', \" mb-2\"),\n                                                                children: display\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            operation && previousValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 \".concat(isRTL ? 'text-left' : 'text-right'),\n                                                                children: [\n                                                                    previousValue,\n                                                                    \" \",\n                                                                    operation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t('angle_mode', {\n                                                                            mode: angleMode === 'deg' ? 'DEG' : 'RAD'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    memory !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"M: \",\n                                                                            memory\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"basic\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: clear,\n                                                                className: \"bg-red-50 hover:bg-red-100 text-red-700\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.slice(0, -1) || '0'),\n                                                                children: \"⌫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('^'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x^y\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sqrt'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"√\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('÷'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xf7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('7'),\n                                                                children: \"7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('8'),\n                                                                children: \"8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('9'),\n                                                                children: \"9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('square'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x\\xb2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('×'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('4'),\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('5'),\n                                                                children: \"5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('6'),\n                                                                children: \"6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('reciprocal'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"1/x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('-'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('1'),\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('2'),\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('3'),\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('factorial'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"n!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('+'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('0'),\n                                                                className: \"col-span-2\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('.'),\n                                                                children: \".\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.startsWith('-') ? display.slice(1) : '-' + display),\n                                                                children: \"\\xb1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: performCalculation,\n                                                                className: \"bg-calculator-primary hover:bg-calculator-primary/90 text-white\",\n                                                                children: \"=\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"advanced\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sin'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"sin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('cos'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"cos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('tan'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"tan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('ln'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"ln\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('log'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"log\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"functions\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-4 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryStore,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryRecall,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryAdd,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"M+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryClear,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"constants\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('pi'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('e'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"e\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        t('history')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: clearHistory,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                                                children: history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 text-center py-4\",\n                                                    children: t('no_history')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 21\n                                                }, undefined) : history.map((calc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs bg-gray-50 p-2 rounded font-mono\",\n                                                        children: calc\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: t('seo.features_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Advanced Mathematical Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Logarithmic & Exponential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature2')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Memory Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature3')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Calculation History\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature4')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Perfect For:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: t('seo.use_cases')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScientificCalculator, \"GUMdVOO4nN+1o+3HBHHOi70/1P0=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = ScientificCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\nvar _c;\n$RefreshReg$(_c, \"ScientificCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ScientificCalculator.tsx\n"));

/***/ })

});