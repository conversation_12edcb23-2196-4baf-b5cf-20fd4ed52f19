
import React, { useState } from 'react';
import { TrendingUp, Calculator, DollarSign, PiggyBank, Home, CreditCard, Target, Users, Briefcase, Building } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';

const FinancialCalculator = () => {
  const { translations, isRTL } = useLanguage();

  // Loan Calculator State
  const [loanPrincipal, setLoanPrincipal] = useState('');
  const [loanRate, setLoanRate] = useState('');
  const [loanTime, setLoanTime] = useState('');
  const [loanResults, setLoanResults] = useState<{
    monthlyPayment: number;
    totalPayment: number;
    totalInterest: number;
  } | null>(null);

  // Investment Calculator State
  const [investmentPrincipal, setInvestmentPrincipal] = useState('');
  const [investmentRate, setInvestmentRate] = useState('');
  const [investmentTime, setInvestmentTime] = useState('');
  const [compoundFrequency, setCompoundFrequency] = useState('12');
  const [investmentResults, setInvestmentResults] = useState<{
    finalAmount: number;
    totalInterest: number;
  } | null>(null);

  // Mortgage Calculator State
  const [homePrice, setHomePrice] = useState('');
  const [downPayment, setDownPayment] = useState('');
  const [mortgageRate, setMortgageRate] = useState('');
  const [mortgageYears, setMortgageYears] = useState('');
  const [mortgageResults, setMortgageResults] = useState<{
    loanAmount: number;
    monthlyPayment: number;
    totalInterest: number;
    totalPayment: number;
  } | null>(null);

  const calculateLoan = () => {
    const P = parseFloat(loanPrincipal);
    const r = parseFloat(loanRate) / 100 / 12; // Monthly interest rate
    const n = parseFloat(loanTime) * 12; // Total number of payments

    if (P && r && n) {
      const monthlyPayment = (P * r * Math.pow(1 + r, n)) / (Math.pow(1 + r, n) - 1);
      const totalPayment = monthlyPayment * n;
      const totalInterest = totalPayment - P;

      setLoanResults({
        monthlyPayment,
        totalPayment,
        totalInterest,
      });
    }
  };

  const calculateInvestment = () => {
    const P = parseFloat(investmentPrincipal);
    const r = parseFloat(investmentRate) / 100;
    const t = parseFloat(investmentTime);
    const n = parseFloat(compoundFrequency);

    if (P && r && t && n) {
      const finalAmount = P * Math.pow(1 + r / n, n * t);
      const totalInterest = finalAmount - P;

      setInvestmentResults({
        finalAmount,
        totalInterest,
      });
    }
  };

  const calculateMortgage = () => {
    const price = parseFloat(homePrice);
    const downPmt = parseFloat(downPayment);
    const loanAmount = price - downPmt;
    const r = parseFloat(mortgageRate) / 100 / 12;
    const n = parseFloat(mortgageYears) * 12;

    if (loanAmount && r && n) {
      const monthlyPayment = (loanAmount * r * Math.pow(1 + r, n)) / (Math.pow(1 + r, n) - 1);
      const totalPayment = monthlyPayment * n;
      const totalInterest = totalPayment - loanAmount;

      setMortgageResults({
        loanAmount,
        monthlyPayment,
        totalInterest,
        totalPayment,
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const tabsConfig = [
    {
      value: 'loan',
      icon: Calculator,
      label: translations['financial.loan'],
    },
    {
      value: 'investment',
      icon: TrendingUp,
      label: translations['financial.investment'],
    },
    {
      value: 'mortgage',
      icon: Home,
      label: translations['financial.mortgage'],
    },
  ];

  const displayedTabs = isRTL ? [...tabsConfig].reverse() : tabsConfig;

  const [activeTab, setActiveTab] = useState('loan');

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Main Calculator Interface */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {translations['financial.title']}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-600 mx-auto rounded-full" />
        </div>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm mb-8">
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3">
              <TrendingUp className="h-8 w-8 text-calculator-primary" />
              {translations['financial.title']}
            </CardTitle>
            <CardDescription className="text-lg text-gray-600">
              Calculate loans, mortgages, investments, and compound interest
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className={`grid w-full grid-cols-4 mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {displayedTabs.map((tab) => (
                  <TabsTrigger key={tab.value} value={tab.value} className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <tab.icon className="h-4 w-4" />
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* Loan Calculator */}
              <TabsContent value="loan">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Calculator className="h-5 w-5 text-green-600" />
                        {translations['financial.loan']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="loanPrincipal" className={isRTL ? 'text-right block' : ''}>{translations['financial.principal']} ($)</Label>
                          <Input
                            id="loanPrincipal"
                            type="number"
                            value={loanPrincipal}
                            onChange={(e) => setLoanPrincipal(e.target.value)}
                            placeholder="10000"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="loanRate" className={isRTL ? 'text-right block' : ''}>{translations['financial.rate']}</Label>
                          <Input
                            id="loanRate"
                            type="number"
                            step="0.01"
                            value={loanRate}
                            onChange={(e) => setLoanRate(e.target.value)}
                            placeholder="5.5"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="loanTime" className={isRTL ? 'text-right block' : ''}>{translations['financial.time']}</Label>
                          <Input
                            id="loanTime"
                            type="number"
                            value={loanTime}
                            onChange={(e) => setLoanTime(e.target.value)}
                            placeholder="5"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                      </div>
                      <Button onClick={calculateLoan} className="w-full bg-green-600 hover:bg-green-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {loanResults && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['common.result']}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="p-4 bg-green-50 rounded-lg">
                            <p className={`text-sm text-green-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.monthly_payment']}</p>
                            <p className={`text-2xl font-bold text-green-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(loanResults.monthlyPayment)}
                            </p>
                          </div>
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <p className={`text-sm text-blue-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.total_payment']}</p>
                            <p className={`text-xl font-bold text-blue-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(loanResults.totalPayment)}
                            </p>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <p className={`text-sm text-orange-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.total_interest']}</p>
                            <p className={`text-xl font-bold text-orange-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(loanResults.totalInterest)}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Investment Calculator */}
              <TabsContent value="investment">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <TrendingUp className="h-5 w-5 text-blue-600" />
                        {translations['financial.compound_interest']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="investmentPrincipal" className={isRTL ? 'text-right block' : ''}>{translations['financial.principal']} ($)</Label>
                          <Input
                            id="investmentPrincipal"
                            type="number"
                            value={investmentPrincipal}
                            onChange={(e) => setInvestmentPrincipal(e.target.value)}
                            placeholder="1000"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="investmentRate" className={isRTL ? 'text-right block' : ''}>{translations['financial.rate']}</Label>
                          <Input
                            id="investmentRate"
                            type="number"
                            step="0.01"
                            value={investmentRate}
                            onChange={(e) => setInvestmentRate(e.target.value)}
                            placeholder="7"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="investmentTime" className={isRTL ? 'text-right block' : ''}>{translations['financial.time']}</Label>
                          <Input
                            id="investmentTime"
                            type="number"
                            value={investmentTime}
                            onChange={(e) => setInvestmentTime(e.target.value)}
                            placeholder="10"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="compoundFrequency" className={isRTL ? 'text-right block' : ''}>{translations['financial.compounding_frequency']}</Label>
                          <select
                            id="compoundFrequency"
                            value={compoundFrequency}
                            onChange={(e) => setCompoundFrequency(e.target.value)}
                            className={`w-full p-2 border border-gray-300 rounded-md ${isRTL ? 'text-right' : ''}`}
                          >
                            <option value="1">{translations['financial.annually']}</option>
                            <option value="2">{translations['financial.semi_annually']}</option>
                            <option value="4">{translations['financial.quarterly']}</option>
                            <option value="12">{translations['financial.monthly']}</option>
                            <option value="365">{translations['financial.daily']}</option>
                          </select>
                        </div>
                      </div>
                      <Button onClick={calculateInvestment} className="w-full bg-blue-600 hover:bg-blue-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {investmentResults && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['common.result']}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <p className={`text-sm text-blue-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.final_amount']}</p>
                            <p className={`text-2xl font-bold text-blue-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(investmentResults.finalAmount)}
                            </p>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <p className={`text-sm text-green-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.interest_earned']}</p>
                            <p className={`text-xl font-bold text-green-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(investmentResults.totalInterest)}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Mortgage Calculator */}
              <TabsContent value="mortgage">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Home className="h-5 w-5 text-purple-600" />
                        {translations['financial.mortgage']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="homePrice" className={isRTL ? 'text-right block' : ''}>{translations['financial.home_price']} ($)</Label>
                          <Input
                            id="homePrice"
                            type="number"
                            value={homePrice}
                            onChange={(e) => setHomePrice(e.target.value)}
                            placeholder="300000"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="downPayment" className={isRTL ? 'text-right block' : ''}>{translations['financial.down_payment']} ($)</Label>
                          <Input
                            id="downPayment"
                            type="number"
                            value={downPayment}
                            onChange={(e) => setDownPayment(e.target.value)}
                            placeholder="60000"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="mortgageRate" className={isRTL ? 'text-right block' : ''}>{translations['financial.rate']}</Label>
                          <Input
                            id="mortgageRate"
                            type="number"
                            step="0.01"
                            value={mortgageRate}
                            onChange={(e) => setMortgageRate(e.target.value)}
                            placeholder="3.5"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="mortgageYears" className={isRTL ? 'text-right block' : ''}>{translations['financial.loan_term']}</Label>
                          <Input
                            id="mortgageYears"
                            type="number"
                            value={mortgageYears}
                            onChange={(e) => setMortgageYears(e.target.value)}
                            placeholder="30"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                      </div>
                      <Button onClick={calculateMortgage} className="w-full bg-purple-600 hover:bg-purple-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {mortgageResults && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['common.result']}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="p-4 bg-purple-50 rounded-lg">
                            <p className={`text-sm text-purple-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.loan_amount']}</p>
                            <p className={`text-xl font-bold text-purple-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(mortgageResults.loanAmount)}
                            </p>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <p className={`text-sm text-green-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.monthly_payment']}</p>
                            <p className={`text-2xl font-bold text-green-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(mortgageResults.monthlyPayment)}
                            </p>
                          </div>
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <p className={`text-sm text-blue-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.total_payment']}</p>
                            <p className={`text-xl font-bold text-blue-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(mortgageResults.totalPayment)}
                            </p>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <p className={`text-sm text-orange-600 ${isRTL ? 'text-right' : ''}`}>{translations['financial.total_interest']}</p>
                            <p className={`text-xl font-bold text-orange-800 ${isRTL ? 'text-right' : ''}`}>
                              {formatCurrency(mortgageResults.totalInterest)}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* SEO Content Section - Moved below the calculator */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {translations['financial.seo.intro']}
            </p>
          </div>
          
          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {translations['financial.seo.tools_title']}
              </h3>
              <div className="space-y-4">
                {[
                  { icon: CreditCard, key: 'financial.seo.tool1' },
                  { icon: Home, key: 'financial.seo.tool2' },
                  { icon: PiggyBank, key: 'financial.seo.tool3' },
                  { icon: TrendingUp, key: 'financial.seo.tool4' }
                ].map((tool, index) => {
                  const Icon = tool.icon;
                  return (
                    <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className="h-6 w-6 text-calculator-primary mt-1 flex-shrink-0" />
                      <p className="text-gray-700">{translations[tool.key]}</p>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Briefcase className="h-6 w-6 text-green-600" />
                Professional Applications
              </h3>
              <p className="text-gray-700 mb-4">
                {translations['financial.seo.benefits']}
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">Financial Advisors</Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-800">Loan Officers</Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-800">Real Estate</Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-800">Investment Planning</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialCalculator;
