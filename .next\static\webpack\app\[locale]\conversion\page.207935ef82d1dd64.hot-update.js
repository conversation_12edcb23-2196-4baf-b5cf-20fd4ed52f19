"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/conversion/page",{

/***/ "(app-pages-browser)/./components/pages/ConversionCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ConversionCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ConversionCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)('conversion');\n    const tUnits = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)('units');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('length');\n    const [fromUnit, setFromUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [toUnit, setToUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const conversions = {\n        length: {\n            meter: {\n                name: tUnits('meter') || 'Meter',\n                factor: 1\n            },\n            kilometer: {\n                name: tUnits('kilometer') || 'Kilometer',\n                factor: 1000\n            },\n            centimeter: {\n                name: tUnits('centimeter') || 'Centimeter',\n                factor: 0.01\n            },\n            millimeter: {\n                name: tUnits('millimeter') || 'Millimeter',\n                factor: 0.001\n            },\n            inch: {\n                name: tUnits('inch') || 'Inch',\n                factor: 0.0254\n            },\n            foot: {\n                name: tUnits('foot') || 'Foot',\n                factor: 0.3048\n            },\n            yard: {\n                name: tUnits('yard') || 'Yard',\n                factor: 0.9144\n            },\n            mile: {\n                name: tUnits('mile') || 'Mile',\n                factor: 1609.34\n            }\n        },\n        weight: {\n            kilogram: {\n                name: tUnits('kilogram') || 'Kilogram',\n                factor: 1\n            },\n            gram: {\n                name: tUnits('gram') || 'Gram',\n                factor: 0.001\n            },\n            pound: {\n                name: tUnits('pound') || 'Pound',\n                factor: 0.453592\n            },\n            ounce: {\n                name: tUnits('ounce') || 'Ounce',\n                factor: 0.0283495\n            },\n            ton: {\n                name: tUnits('ton') || 'Ton',\n                factor: 1000\n            }\n        },\n        temperature: {\n            celsius: {\n                name: tUnits('celsius') || 'Celsius',\n                factor: 1\n            },\n            fahrenheit: {\n                name: tUnits('fahrenheit') || 'Fahrenheit',\n                factor: 1\n            },\n            kelvin: {\n                name: tUnits('kelvin') || 'Kelvin',\n                factor: 1\n            }\n        },\n        area: {\n            square_meter: {\n                name: t('units.square_meter') || 'Square Meter',\n                factor: 1\n            },\n            square_kilometer: {\n                name: t('units.square_kilometer') || 'Square Kilometer',\n                factor: 1000000\n            },\n            square_centimeter: {\n                name: t('units.square_centimeter') || 'Square Centimeter',\n                factor: 0.0001\n            },\n            square_inch: {\n                name: t('units.square_inch') || 'Square Inch',\n                factor: 0.00064516\n            },\n            square_foot: {\n                name: t('units.square_foot') || 'Square Foot',\n                factor: 0.092903\n            },\n            acre: {\n                name: t('units.acre') || 'Acre',\n                factor: 4046.86\n            },\n            hectare: {\n                name: t('units.hectare') || 'Hectare',\n                factor: 10000\n            }\n        },\n        volume: {\n            liter: {\n                name: t('units.liter') || 'Liter',\n                factor: 1\n            },\n            milliliter: {\n                name: t('units.milliliter') || 'Milliliter',\n                factor: 0.001\n            },\n            cubic_meter: {\n                name: t('units.cubic_meter') || 'Cubic Meter',\n                factor: 1000\n            },\n            cubic_centimeter: {\n                name: t('units.cubic_centimeter') || 'Cubic Centimeter',\n                factor: 0.001\n            },\n            gallon: {\n                name: t('units.gallon') || 'Gallon',\n                factor: 3.78541\n            },\n            quart: {\n                name: t('units.quart') || 'Quart',\n                factor: 0.946353\n            },\n            pint: {\n                name: t('units.pint') || 'Pint',\n                factor: 0.473176\n            },\n            cup: {\n                name: t('units.cup') || 'Cup',\n                factor: 0.236588\n            }\n        },\n        speed: {\n            meter_per_second: {\n                name: t('units.meter_per_second') || 'Meter/Second',\n                factor: 1\n            },\n            kilometer_per_hour: {\n                name: t('units.kilometer_per_hour') || 'Kilometer/Hour',\n                factor: 0.277778\n            },\n            mile_per_hour: {\n                name: t('units.mile_per_hour') || 'Mile/Hour',\n                factor: 0.44704\n            },\n            foot_per_second: {\n                name: t('units.foot_per_second') || 'Foot/Second',\n                factor: 0.3048\n            },\n            knot: {\n                name: t('units.knot') || 'Knot',\n                factor: 0.514444\n            }\n        },\n        energy: {\n            joule: {\n                name: t('units.joule') || 'Joule',\n                factor: 1\n            },\n            kilojoule: {\n                name: t('units.kilojoule') || 'Kilojoule',\n                factor: 1000\n            },\n            calorie: {\n                name: t('units.calorie') || 'Calorie',\n                factor: 4.184\n            },\n            kilocalorie: {\n                name: t('units.kilocalorie') || 'Kilocalorie',\n                factor: 4184\n            },\n            watt_hour: {\n                name: t('units.watt_hour') || 'Watt Hour',\n                factor: 3600\n            },\n            kilowatt_hour: {\n                name: t('units.kilowatt_hour') || 'Kilowatt Hour',\n                factor: 3600000\n            }\n        },\n        pressure: {\n            pascal: {\n                name: t('units.pascal') || 'Pascal',\n                factor: 1\n            },\n            kilopascal: {\n                name: t('units.kilopascal') || 'Kilopascal',\n                factor: 1000\n            },\n            bar: {\n                name: t('units.bar') || 'Bar',\n                factor: 100000\n            },\n            atmosphere: {\n                name: t('units.atmosphere') || 'Atmosphere',\n                factor: 101325\n            },\n            psi: {\n                name: t('units.psi') || 'PSI',\n                factor: 6894.76\n            },\n            torr: {\n                name: t('units.torr') || 'Torr',\n                factor: 133.322\n            }\n        },\n        power: {\n            watt: {\n                name: t('units.watt') || 'Watt',\n                factor: 1\n            },\n            kilowatt: {\n                name: t('units.kilowatt') || 'Kilowatt',\n                factor: 1000\n            },\n            horsepower: {\n                name: t('units.horsepower') || 'Horsepower',\n                factor: 745.7\n            },\n            btu_per_hour: {\n                name: t('units.btu_per_hour') || 'BTU/Hour',\n                factor: 0.293071\n            }\n        }\n    };\n    const handleConvert = ()=>{\n        if (!inputValue || !fromUnit || !toUnit) return;\n        const value = parseFloat(inputValue);\n        if (isNaN(value)) return;\n        let convertedValue;\n        if (category === 'temperature') {\n            // Special handling for temperature conversions\n            if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {\n                convertedValue = value * 9 / 5 + 32;\n            } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {\n                convertedValue = (value - 32) * 5 / 9;\n            } else if (fromUnit === 'celsius' && toUnit === 'kelvin') {\n                convertedValue = value + 273.15;\n            } else if (fromUnit === 'kelvin' && toUnit === 'celsius') {\n                convertedValue = value - 273.15;\n            } else if (fromUnit === 'fahrenheit' && toUnit === 'kelvin') {\n                convertedValue = (value - 32) * 5 / 9 + 273.15;\n            } else if (fromUnit === 'kelvin' && toUnit === 'fahrenheit') {\n                convertedValue = (value - 273.15) * 9 / 5 + 32;\n            } else {\n                convertedValue = value; // Same unit\n            }\n        } else {\n            var _conversions_category_fromUnit, _conversions_category_toUnit;\n            // Regular unit conversions using factors\n            const fromFactor = ((_conversions_category_fromUnit = conversions[category][fromUnit]) === null || _conversions_category_fromUnit === void 0 ? void 0 : _conversions_category_fromUnit.factor) || 1;\n            const toFactor = ((_conversions_category_toUnit = conversions[category][toUnit]) === null || _conversions_category_toUnit === void 0 ? void 0 : _conversions_category_toUnit.factor) || 1;\n            convertedValue = value * fromFactor / toFactor;\n        }\n        setResult(convertedValue.toFixed(6));\n    };\n    const categories = [\n        {\n            key: 'length',\n            name: t('length') || 'Length'\n        },\n        {\n            key: 'weight',\n            name: t('weight') || 'Weight'\n        },\n        {\n            key: 'temperature',\n            name: t('temperature') || 'Temperature'\n        },\n        {\n            key: 'area',\n            name: t('area') || 'Area'\n        },\n        {\n            key: 'volume',\n            name: t('volume') || 'Volume'\n        },\n        {\n            key: 'speed',\n            name: t('speed') || 'Speed'\n        },\n        {\n            key: 'energy',\n            name: t('energy') || 'Energy'\n        },\n        {\n            key: 'pressure',\n            name: t('pressure') || 'Pressure'\n        },\n        {\n            key: 'power',\n            name: t('power') || 'Power'\n        }\n    ];\n    // Reset units when category changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ConversionCalculator.useEffect\": ()=>{\n            setFromUnit('');\n            setToUnit('');\n            setResult('');\n        }\n    }[\"ConversionCalculator.useEffect\"], [\n        category\n    ]);\n    const categoryFeatures = [\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            key: 'seo.category1'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            key: 'seo.category2'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            key: 'seo.category3'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            key: 'seo.category4'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            key: 'seo.category5'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-gradient-to-r from-orange-500 to-red-600 mx-auto rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-2xl text-center\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('category')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: category,\n                                            onValueChange: setCategory,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: cat.key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: cat.name\n                                                        }, cat.key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('value')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"number\",\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            placeholder: t('enter_value'),\n                                            className: isRTL ? 'text-right' : 'text-left'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('from')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: fromUnit,\n                                            onValueChange: setFromUnit,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: Object.entries(conversions[category] || {}).map((param)=>{\n                                                        let [key, unit] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: unit.name\n                                                        }, key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('to')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: toUnit,\n                                            onValueChange: setToUnit,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: Object.entries(conversions[category] || {}).map((param)=>{\n                                                        let [key, unit] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: unit.name\n                                                        }, key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleConvert,\n                                    className: \"w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700\",\n                                    children: t('calculate') || 'Calculate'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('result') || 'Result'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-orange-600 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: result\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 leading-relaxed mb-6\",\n                                children: t('seo.intro')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: t('seo.categories_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: categoryFeatures.map((feature, index)=>{\n                                    const Icon = feature.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 \".concat(isRTL ? 'flex-row-reverse' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-6 w-6 text-orange-500 mt-1 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700\",\n                                                children: t(feature.key)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 italic\",\n                                children: t('seo.applications')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConversionCalculator, \"1NspxKjxFEK+4jJtZSfHy0UvmOc=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale\n    ];\n});\n_c = ConversionCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConversionCalculator);\nvar _c;\n$RefreshReg$(_c, \"ConversionCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ConversionCalculator.tsx\n"));

/***/ })

});