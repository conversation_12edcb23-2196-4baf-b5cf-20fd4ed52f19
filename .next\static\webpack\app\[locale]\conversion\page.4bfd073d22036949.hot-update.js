"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/conversion/page",{

/***/ "(app-pages-browser)/./components/pages/ConversionCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ConversionCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,CheckCircle,Globe,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ConversionCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)('conversion');\n    const tUnits = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)('units');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('length');\n    const [fromUnit, setFromUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [toUnit, setToUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const conversions = {\n        length: {\n            meter: {\n                name: tUnits('meter') || 'Meter',\n                factor: 1\n            },\n            kilometer: {\n                name: tUnits('kilometer') || 'Kilometer',\n                factor: 1000\n            },\n            centimeter: {\n                name: tUnits('centimeter') || 'Centimeter',\n                factor: 0.01\n            },\n            millimeter: {\n                name: tUnits('millimeter') || 'Millimeter',\n                factor: 0.001\n            },\n            inch: {\n                name: tUnits('inch') || 'Inch',\n                factor: 0.0254\n            },\n            foot: {\n                name: tUnits('foot') || 'Foot',\n                factor: 0.3048\n            },\n            yard: {\n                name: tUnits('yard') || 'Yard',\n                factor: 0.9144\n            },\n            mile: {\n                name: tUnits('mile') || 'Mile',\n                factor: 1609.34\n            }\n        },\n        weight: {\n            kilogram: {\n                name: tUnits('kilogram') || 'Kilogram',\n                factor: 1\n            },\n            gram: {\n                name: tUnits('gram') || 'Gram',\n                factor: 0.001\n            },\n            pound: {\n                name: tUnits('pound') || 'Pound',\n                factor: 0.453592\n            },\n            ounce: {\n                name: tUnits('ounce') || 'Ounce',\n                factor: 0.0283495\n            },\n            ton: {\n                name: tUnits('ton') || 'Ton',\n                factor: 1000\n            }\n        },\n        temperature: {\n            celsius: {\n                name: tUnits('celsius') || 'Celsius',\n                factor: 1\n            },\n            fahrenheit: {\n                name: tUnits('fahrenheit') || 'Fahrenheit',\n                factor: 1\n            },\n            kelvin: {\n                name: tUnits('kelvin') || 'Kelvin',\n                factor: 1\n            }\n        },\n        area: {\n            square_meter: {\n                name: tUnits('square_meter') || 'Square Meter',\n                factor: 1\n            },\n            square_kilometer: {\n                name: tUnits('square_kilometer') || 'Square Kilometer',\n                factor: 1000000\n            },\n            square_centimeter: {\n                name: tUnits('square_centimeter') || 'Square Centimeter',\n                factor: 0.0001\n            },\n            square_inch: {\n                name: tUnits('square_inch') || 'Square Inch',\n                factor: 0.00064516\n            },\n            square_foot: {\n                name: tUnits('square_foot') || 'Square Foot',\n                factor: 0.092903\n            },\n            acre: {\n                name: tUnits('acre') || 'Acre',\n                factor: 4046.86\n            },\n            hectare: {\n                name: tUnits('hectare') || 'Hectare',\n                factor: 10000\n            }\n        },\n        volume: {\n            liter: {\n                name: tUnits('liter') || 'Liter',\n                factor: 1\n            },\n            milliliter: {\n                name: tUnits('milliliter') || 'Milliliter',\n                factor: 0.001\n            },\n            cubic_meter: {\n                name: tUnits('cubic_meter') || 'Cubic Meter',\n                factor: 1000\n            },\n            cubic_centimeter: {\n                name: tUnits('cubic_centimeter') || 'Cubic Centimeter',\n                factor: 0.001\n            },\n            gallon: {\n                name: tUnits('gallon') || 'Gallon',\n                factor: 3.78541\n            },\n            quart: {\n                name: tUnits('quart') || 'Quart',\n                factor: 0.946353\n            },\n            pint: {\n                name: tUnits('pint') || 'Pint',\n                factor: 0.473176\n            },\n            cup: {\n                name: tUnits('cup') || 'Cup',\n                factor: 0.236588\n            }\n        },\n        speed: {\n            meter_per_second: {\n                name: tUnits('meter_per_second') || 'Meter/Second',\n                factor: 1\n            },\n            kilometer_per_hour: {\n                name: tUnits('kilometer_per_hour') || 'Kilometer/Hour',\n                factor: 0.277778\n            },\n            mile_per_hour: {\n                name: tUnits('mile_per_hour') || 'Mile/Hour',\n                factor: 0.44704\n            },\n            foot_per_second: {\n                name: tUnits('foot_per_second') || 'Foot/Second',\n                factor: 0.3048\n            },\n            knot: {\n                name: tUnits('knot') || 'Knot',\n                factor: 0.514444\n            }\n        },\n        energy: {\n            joule: {\n                name: tUnits('joule') || 'Joule',\n                factor: 1\n            },\n            kilojoule: {\n                name: tUnits('kilojoule') || 'Kilojoule',\n                factor: 1000\n            },\n            calorie: {\n                name: tUnits('calorie') || 'Calorie',\n                factor: 4.184\n            },\n            kilocalorie: {\n                name: tUnits('kilocalorie') || 'Kilocalorie',\n                factor: 4184\n            },\n            watt_hour: {\n                name: tUnits('watt_hour') || 'Watt Hour',\n                factor: 3600\n            },\n            kilowatt_hour: {\n                name: tUnits('kilowatt_hour') || 'Kilowatt Hour',\n                factor: 3600000\n            }\n        },\n        pressure: {\n            pascal: {\n                name: tUnits('pascal') || 'Pascal',\n                factor: 1\n            },\n            kilopascal: {\n                name: tUnits('kilopascal') || 'Kilopascal',\n                factor: 1000\n            },\n            bar: {\n                name: tUnits('bar') || 'Bar',\n                factor: 100000\n            },\n            atmosphere: {\n                name: tUnits('atmosphere') || 'Atmosphere',\n                factor: 101325\n            },\n            psi: {\n                name: tUnits('psi') || 'PSI',\n                factor: 6894.76\n            },\n            torr: {\n                name: tUnits('torr') || 'Torr',\n                factor: 133.322\n            }\n        },\n        power: {\n            watt: {\n                name: tUnits('watt') || 'Watt',\n                factor: 1\n            },\n            kilowatt: {\n                name: tUnits('kilowatt') || 'Kilowatt',\n                factor: 1000\n            },\n            horsepower: {\n                name: tUnits('horsepower') || 'Horsepower',\n                factor: 745.7\n            },\n            btu_per_hour: {\n                name: tUnits('btu_per_hour') || 'BTU/Hour',\n                factor: 0.293071\n            }\n        }\n    };\n    const handleConvert = ()=>{\n        if (!inputValue || !fromUnit || !toUnit) return;\n        const value = parseFloat(inputValue);\n        if (isNaN(value)) return;\n        let convertedValue;\n        if (category === 'temperature') {\n            // Special handling for temperature conversions\n            if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {\n                convertedValue = value * 9 / 5 + 32;\n            } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {\n                convertedValue = (value - 32) * 5 / 9;\n            } else if (fromUnit === 'celsius' && toUnit === 'kelvin') {\n                convertedValue = value + 273.15;\n            } else if (fromUnit === 'kelvin' && toUnit === 'celsius') {\n                convertedValue = value - 273.15;\n            } else if (fromUnit === 'fahrenheit' && toUnit === 'kelvin') {\n                convertedValue = (value - 32) * 5 / 9 + 273.15;\n            } else if (fromUnit === 'kelvin' && toUnit === 'fahrenheit') {\n                convertedValue = (value - 273.15) * 9 / 5 + 32;\n            } else {\n                convertedValue = value; // Same unit\n            }\n        } else {\n            var _conversions_category_fromUnit, _conversions_category_toUnit;\n            // Regular unit conversions using factors\n            const fromFactor = ((_conversions_category_fromUnit = conversions[category][fromUnit]) === null || _conversions_category_fromUnit === void 0 ? void 0 : _conversions_category_fromUnit.factor) || 1;\n            const toFactor = ((_conversions_category_toUnit = conversions[category][toUnit]) === null || _conversions_category_toUnit === void 0 ? void 0 : _conversions_category_toUnit.factor) || 1;\n            convertedValue = value * fromFactor / toFactor;\n        }\n        setResult(convertedValue.toFixed(6));\n    };\n    const categories = [\n        {\n            key: 'length',\n            name: t('length') || 'Length'\n        },\n        {\n            key: 'weight',\n            name: t('weight') || 'Weight'\n        },\n        {\n            key: 'temperature',\n            name: t('temperature') || 'Temperature'\n        },\n        {\n            key: 'area',\n            name: t('area') || 'Area'\n        },\n        {\n            key: 'volume',\n            name: t('volume') || 'Volume'\n        },\n        {\n            key: 'speed',\n            name: t('speed') || 'Speed'\n        },\n        {\n            key: 'energy',\n            name: t('energy') || 'Energy'\n        },\n        {\n            key: 'pressure',\n            name: t('pressure') || 'Pressure'\n        },\n        {\n            key: 'power',\n            name: t('power') || 'Power'\n        }\n    ];\n    // Reset units when category changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ConversionCalculator.useEffect\": ()=>{\n            setFromUnit('');\n            setToUnit('');\n            setResult('');\n        }\n    }[\"ConversionCalculator.useEffect\"], [\n        category\n    ]);\n    const categoryFeatures = [\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            key: 'seo.category1'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            key: 'seo.category2'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            key: 'seo.category3'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            key: 'seo.category4'\n        },\n        {\n            icon: _barrel_optimize_names_Calculator_CheckCircle_Globe_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            key: 'seo.category5'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-gradient-to-r from-orange-500 to-red-600 mx-auto rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-2xl text-center\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('category')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: category,\n                                            onValueChange: setCategory,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: cat.key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: cat.name\n                                                        }, cat.key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('value')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"number\",\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            placeholder: t('enter_value'),\n                                            className: isRTL ? 'text-right' : 'text-left'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('from')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: fromUnit,\n                                            onValueChange: setFromUnit,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: Object.entries(conversions[category] || {}).map((param)=>{\n                                                        let [key, unit] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: unit.name\n                                                        }, key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('to')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: toUnit,\n                                            onValueChange: setToUnit,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    className: isRTL ? 'text-right' : 'text-left',\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    dir: isRTL ? 'rtl' : 'ltr',\n                                                    className: \"bg-white border border-gray-200 shadow-lg z-50\",\n                                                    children: Object.entries(conversions[category] || {}).map((param)=>{\n                                                        let [key, unit] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: key,\n                                                            className: isRTL ? 'text-right' : 'text-left',\n                                                            children: unit.name\n                                                        }, key, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleConvert,\n                                    className: \"w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700\",\n                                    children: t('calculate') || 'Calculate'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: t('result') || 'Result'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-orange-600 \".concat(isRTL ? 'text-right' : 'text-left'),\n                                            children: result\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 leading-relaxed mb-6\",\n                                children: t('seo.intro')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: t('seo.categories_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: categoryFeatures.map((feature, index)=>{\n                                    const Icon = feature.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 \".concat(isRTL ? 'flex-row-reverse' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-6 w-6 text-orange-500 mt-1 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700\",\n                                                children: t(feature.key)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 italic\",\n                                children: t('seo.applications')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ConversionCalculator.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConversionCalculator, \"1NspxKjxFEK+4jJtZSfHy0UvmOc=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale\n    ];\n});\n_c = ConversionCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConversionCalculator);\nvar _c;\n$RefreshReg$(_c, \"ConversionCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ConversionCalculator.tsx\n"));

/***/ })

});