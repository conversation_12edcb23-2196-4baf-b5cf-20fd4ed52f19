"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/scientific/page",{

/***/ "(app-pages-browser)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ScientificCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const isRTL = locale === 'ar';\n    // Calculator state\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Helper functions\n    const toRadians = (degrees)=>degrees * (Math.PI / 180);\n    const toDegrees = (radians)=>radians * (180 / Math.PI);\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[addToHistory]\": (calculation)=>{\n            setHistory({\n                \"ScientificCalculator.useCallback[addToHistory]\": (prev)=>[\n                        calculation,\n                        ...prev.slice(0, 9)\n                    ]\n            }[\"ScientificCalculator.useCallback[addToHistory]\"]);\n        }\n    }[\"ScientificCalculator.useCallback[addToHistory]\"], []);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' && num !== '.' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performCalculation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performCalculation]\": ()=>{\n            const inputValue = parseFloat(display);\n            if (previousValue !== null && operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(null);\n                setOperation(null);\n                setWaitingForNewValue(true);\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n        }\n    }[\"ScientificCalculator.useCallback[performCalculation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const performFunction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performFunction]\": (func)=>{\n            const value = parseFloat(display);\n            let result;\n            switch(func){\n                case 'sin':\n                    result = Math.sin(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'cos':\n                    result = Math.cos(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'tan':\n                    result = Math.tan(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'ln':\n                    result = Math.log(value);\n                    break;\n                case 'log':\n                    result = Math.log10(value);\n                    break;\n                case 'sqrt':\n                    result = Math.sqrt(value);\n                    break;\n                case 'square':\n                    result = value * value;\n                    break;\n                case 'factorial':\n                    result = value <= 0 ? 1 : Array.from({\n                        length: value\n                    }, {\n                        \"ScientificCalculator.useCallback[performFunction]\": (_, i)=>i + 1\n                    }[\"ScientificCalculator.useCallback[performFunction]\"]).reduce({\n                        \"ScientificCalculator.useCallback[performFunction]\": (a, b)=>a * b\n                    }[\"ScientificCalculator.useCallback[performFunction]\"], 1);\n                    break;\n                case 'reciprocal':\n                    result = 1 / value;\n                    break;\n                default:\n                    result = value;\n            }\n            setDisplay(String(result));\n            setWaitingForNewValue(true);\n            addToHistory(\"\".concat(func, \"(\").concat(value, \") = \").concat(result));\n        }\n    }[\"ScientificCalculator.useCallback[performFunction]\"], [\n        display,\n        angleMode,\n        addToHistory\n    ]);\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'pi':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    t('calculator')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: angleMode === 'deg' ? 'default' : 'secondary',\n                                                        children: t('angle_mode', {\n                                                            mode: angleMode === 'deg' ? t('degree') : t('radian')\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setAngleMode(angleMode === 'deg' ? 'rad' : 'deg'),\n                                                        children: angleMode === 'deg' ? 'RAD' : 'DEG'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: copyResult,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            defaultValue: \"basic\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                                    className: \"grid w-full grid-cols-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"basic\",\n                                                            children: t('basic')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"functions\",\n                                                            children: t('functions')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"constants\",\n                                                            children: t('constants')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-mono \".concat(isRTL ? 'text-left' : 'text-right', \" mb-2\"),\n                                                                children: display\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            operation && previousValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 \".concat(isRTL ? 'text-left' : 'text-right'),\n                                                                children: [\n                                                                    previousValue,\n                                                                    \" \",\n                                                                    operation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t('angle_mode', {\n                                                                            mode: angleMode === 'deg' ? 'DEG' : 'RAD'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    memory !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"M: \",\n                                                                            memory\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"basic\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: clear,\n                                                                className: \"bg-red-50 hover:bg-red-100 text-red-700\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.slice(0, -1) || '0'),\n                                                                children: \"⌫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('^'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x^y\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sqrt'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"√\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('÷'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xf7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('7'),\n                                                                children: \"7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('8'),\n                                                                children: \"8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('9'),\n                                                                children: \"9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('square'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x\\xb2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('×'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('4'),\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('5'),\n                                                                children: \"5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('6'),\n                                                                children: \"6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('reciprocal'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"1/x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('-'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('1'),\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('2'),\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('3'),\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('factorial'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"n!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('+'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('0'),\n                                                                className: \"col-span-2\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('.'),\n                                                                children: \".\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.startsWith('-') ? display.slice(1) : '-' + display),\n                                                                children: \"\\xb1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: performCalculation,\n                                                                className: \"bg-calculator-primary hover:bg-calculator-primary/90 text-white\",\n                                                                children: \"=\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"advanced\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sin'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"sin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('cos'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"cos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('tan'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"tan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('ln'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"ln\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('log'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"log\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"functions\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-4 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryStore,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryRecall,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryAdd,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"M+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryClear,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"constants\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('pi'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('e'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"e\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        t('history')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: clearHistory,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                                                children: history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 text-center py-4\",\n                                                    children: t('no_history')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined) : history.map((calc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs bg-gray-50 p-2 rounded font-mono\",\n                                                        children: calc\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: t('seo.features_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Advanced Mathematical Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Logarithmic & Exponential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature2')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Memory Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature3')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Calculation History\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature4')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Perfect For:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: t('seo.use_cases')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScientificCalculator, \"EyLPQnZffevf5NtWITwAKx/Q6f0=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = ScientificCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\nvar _c;\n$RefreshReg$(_c, \"ScientificCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ScientificCalculator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Te),\n/* harmony export */   toast: () => (/* binding */ Jt),\n/* harmony export */   useSonner: () => (/* binding */ we)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nvar Ct = (s)=>{\n    switch(s){\n        case \"success\":\n            return $t;\n        case \"info\":\n            return _t;\n        case \"warning\":\n            return Wt;\n        case \"error\":\n            return Ut;\n        default:\n            return null;\n    }\n}, Ft = Array(12).fill(0), It = (param)=>{\n    let { visible: s } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-loading-wrapper\",\n        \"data-visible\": s\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, Ft.map((o, t)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(t)\n        }))));\n}, $t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), Wt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), _t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), Ut = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\n\nvar Dt = ()=>{\n    _s();\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Dt.useEffect\": ()=>{\n            let t = {\n                \"Dt.useEffect.t\": ()=>{\n                    o(document.hidden);\n                }\n            }[\"Dt.useEffect.t\"];\n            return document.addEventListener(\"visibilitychange\", t), ({\n                \"Dt.useEffect\": ()=>window.removeEventListener(\"visibilitychange\", t)\n            })[\"Dt.useEffect\"];\n        }\n    }[\"Dt.useEffect\"], []), s;\n};\n_s(Dt, \"jaQSwt4vxaqRXP1w1oZTb+hcdss=\");\n_c = Dt;\nvar ct = 1, ut = class {\n    constructor(){\n        this.subscribe = (o)=>(this.subscribers.push(o), ()=>{\n                let t = this.subscribers.indexOf(o);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (o)=>{\n            this.subscribers.forEach((t)=>t(o));\n        };\n        this.addToast = (o)=>{\n            this.publish(o), this.toasts = [\n                ...this.toasts,\n                o\n            ];\n        };\n        this.create = (o)=>{\n            var b;\n            let { message: t, ...n } = o, h = typeof (o == null ? void 0 : o.id) == \"number\" || ((b = o.id) == null ? void 0 : b.length) > 0 ? o.id : ct++, u = this.toasts.find((d)=>d.id === h), g = o.dismissible === void 0 ? !0 : o.dismissible;\n            return u ? this.toasts = this.toasts.map((d)=>d.id === h ? (this.publish({\n                    ...d,\n                    ...o,\n                    id: h,\n                    title: t\n                }), {\n                    ...d,\n                    ...o,\n                    id: h,\n                    dismissible: g,\n                    title: t\n                }) : d) : this.addToast({\n                title: t,\n                ...n,\n                dismissible: g,\n                id: h\n            }), h;\n        };\n        this.dismiss = (o)=>(o || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((n)=>n({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: o,\n                    dismiss: !0\n                })), o);\n        this.message = (o, t)=>this.create({\n                ...t,\n                message: o\n            });\n        this.error = (o, t)=>this.create({\n                ...t,\n                message: o,\n                type: \"error\"\n            });\n        this.success = (o, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: o\n            });\n        this.info = (o, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: o\n            });\n        this.warning = (o, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: o\n            });\n        this.loading = (o, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: o\n            });\n        this.promise = (o, t)=>{\n            if (!t) return;\n            let n;\n            t.loading !== void 0 && (n = this.create({\n                ...t,\n                promise: o,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let h = o instanceof Promise ? o : o(), u = n !== void 0;\n            return h.then(async (g)=>{\n                if (Ot(g) && !g.ok) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(\"HTTP error! status: \".concat(g.status)) : t.error, d = typeof t.description == \"function\" ? await t.description(\"HTTP error! status: \".concat(g.status)) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                } else if (t.success !== void 0) {\n                    u = !1;\n                    let b = typeof t.success == \"function\" ? await t.success(g) : t.success, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"success\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).catch(async (g)=>{\n                if (t.error !== void 0) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(g) : t.error, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).finally(()=>{\n                var g;\n                u && (this.dismiss(n), n = void 0), (g = t.finally) == null || g.call(t);\n            }), n;\n        };\n        this.custom = (o, t)=>{\n            let n = (t == null ? void 0 : t.id) || ct++;\n            return this.create({\n                jsx: o(n),\n                id: n,\n                ...t\n            }), n;\n        };\n        this.subscribers = [], this.toasts = [];\n    }\n}, v = new ut, Vt = (s, o)=>{\n    let t = (o == null ? void 0 : o.id) || ct++;\n    return v.addToast({\n        title: s,\n        ...o,\n        id: t\n    }), t;\n}, Ot = (s)=>s && typeof s == \"object\" && \"ok\" in s && typeof s.ok == \"boolean\" && \"status\" in s && typeof s.status == \"number\", Kt = Vt, Xt = ()=>v.toasts, Jt = Object.assign(Kt, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: Xt\n});\nfunction ft(s) {\n    let { insertAt: o } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    if (!s || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], n = document.createElement(\"style\");\n    n.type = \"text/css\", o === \"top\" && t.firstChild ? t.insertBefore(n, t.firstChild) : t.appendChild(n), n.styleSheet ? n.styleSheet.cssText = s : n.appendChild(document.createTextNode(s));\n}\nft(':where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position=\"right\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\"left\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n');\nfunction U(s) {\n    return s.label !== void 0;\n}\n_c1 = U;\nvar qt = 3, Qt = \"32px\", Zt = 4e3, te = 356, ee = 14, oe = 20, ae = 200;\nfunction ne() {\n    for(var _len = arguments.length, s = new Array(_len), _key = 0; _key < _len; _key++){\n        s[_key] = arguments[_key];\n    }\n    return s.filter(Boolean).join(\" \");\n}\nvar se = (s)=>{\n    _s1();\n    var yt, xt, vt, wt, Tt, St, Rt, Et, Nt, Pt;\n    let { invert: o, toast: t, unstyled: n, interacting: h, setHeights: u, visibleToasts: g, heights: b, index: d, toasts: q, expanded: $, removeToast: V, defaultRichColors: Q, closeButton: i, style: O, cancelButtonStyle: K, actionButtonStyle: Z, className: tt = \"\", descriptionClassName: et = \"\", duration: X, position: ot, gap: w, loadingIcon: j, expandByDefault: W, classNames: r, icons: I, closeButtonAriaLabel: at = \"Close toast\", pauseWhenPageIsHidden: k, cn: T } = s, [z, nt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [D, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [st, N] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [M, rt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [c, m] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [y, S] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), l = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), _ = d === 0, J = d + 1 <= g, x = t.type, P = t.dismissible !== !1, Mt = t.className || \"\", At = t.descriptionClassName || \"\", G = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[G]\": ()=>b.findIndex({\n                \"se.useMemo[G]\": (a)=>a.toastId === t.id\n            }[\"se.useMemo[G]\"]) || 0\n    }[\"se.useMemo[G]\"], [\n        b,\n        t.id\n    ]), Lt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[Lt]\": ()=>{\n            var a;\n            return (a = t.closeButton) != null ? a : i;\n        }\n    }[\"se.useMemo[Lt]\"], [\n        t.closeButton,\n        i\n    ]), mt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[mt]\": ()=>t.duration || X || Zt\n    }[\"se.useMemo[mt]\"], [\n        t.duration,\n        X\n    ]), it = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), Y = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), pt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), F = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [gt, zt] = ot.split(\"-\"), ht = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[ht]\": ()=>b.reduce({\n                \"se.useMemo[ht]\": (a, f, p)=>p >= G ? a : a + f.height\n            }[\"se.useMemo[ht]\"], 0)\n    }[\"se.useMemo[ht]\"], [\n        b,\n        G\n    ]), bt = Dt(), jt = t.invert || o, lt = x === \"loading\";\n    Y.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo\": ()=>G * w + ht\n    }[\"se.useMemo\"], [\n        G,\n        ht\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            nt(!0);\n        }\n    }[\"se.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"se.useLayoutEffect\": ()=>{\n            if (!z) return;\n            let a = l.current, f = a.style.height;\n            a.style.height = \"auto\";\n            let p = a.getBoundingClientRect().height;\n            a.style.height = f, S(p), u({\n                \"se.useLayoutEffect\": (B)=>B.find({\n                        \"se.useLayoutEffect\": (R)=>R.toastId === t.id\n                    }[\"se.useLayoutEffect\"]) ? B.map({\n                        \"se.useLayoutEffect\": (R)=>R.toastId === t.id ? {\n                                ...R,\n                                height: p\n                            } : R\n                    }[\"se.useLayoutEffect\"]) : [\n                        {\n                            toastId: t.id,\n                            height: p,\n                            position: t.position\n                        },\n                        ...B\n                    ]\n            }[\"se.useLayoutEffect\"]);\n        }\n    }[\"se.useLayoutEffect\"], [\n        z,\n        t.title,\n        t.description,\n        u,\n        t.id\n    ]);\n    let L = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"se.useCallback[L]\": ()=>{\n            H(!0), m(Y.current), u({\n                \"se.useCallback[L]\": (a)=>a.filter({\n                        \"se.useCallback[L]\": (f)=>f.toastId !== t.id\n                    }[\"se.useCallback[L]\"])\n            }[\"se.useCallback[L]\"]), setTimeout({\n                \"se.useCallback[L]\": ()=>{\n                    V(t);\n                }\n            }[\"se.useCallback[L]\"], ae);\n        }\n    }[\"se.useCallback[L]\"], [\n        t,\n        V,\n        u,\n        Y\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            if (t.promise && x === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n            let a, f = mt;\n            return $ || h || k && bt ? ({\n                \"se.useEffect\": ()=>{\n                    if (pt.current < it.current) {\n                        let C = new Date().getTime() - it.current;\n                        f = f - C;\n                    }\n                    pt.current = new Date().getTime();\n                }\n            })[\"se.useEffect\"]() : ({\n                \"se.useEffect\": ()=>{\n                    f !== 1 / 0 && (it.current = new Date().getTime(), a = setTimeout({\n                        \"se.useEffect\": ()=>{\n                            var C;\n                            (C = t.onAutoClose) == null || C.call(t, t), L();\n                        }\n                    }[\"se.useEffect\"], f));\n                }\n            })[\"se.useEffect\"](), ({\n                \"se.useEffect\": ()=>clearTimeout(a)\n            })[\"se.useEffect\"];\n        }\n    }[\"se.useEffect\"], [\n        $,\n        h,\n        W,\n        t,\n        mt,\n        L,\n        t.promise,\n        x,\n        k,\n        bt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            let a = l.current;\n            if (a) {\n                let f = a.getBoundingClientRect().height;\n                return S(f), u({\n                    \"se.useEffect\": (p)=>[\n                            {\n                                toastId: t.id,\n                                height: f,\n                                position: t.position\n                            },\n                            ...p\n                        ]\n                }[\"se.useEffect\"]), ({\n                    \"se.useEffect\": ()=>u({\n                            \"se.useEffect\": (p)=>p.filter({\n                                    \"se.useEffect\": (B)=>B.toastId !== t.id\n                                }[\"se.useEffect\"])\n                        }[\"se.useEffect\"])\n                })[\"se.useEffect\"];\n            }\n        }\n    }[\"se.useEffect\"], [\n        u,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            t.delete && L();\n        }\n    }[\"se.useEffect\"], [\n        L,\n        t.delete\n    ]);\n    function Yt() {\n        return I != null && I.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, I.loading) : j ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, j) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(It, {\n            visible: x === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        \"aria-live\": t.important ? \"assertive\" : \"polite\",\n        \"aria-atomic\": \"true\",\n        role: \"status\",\n        tabIndex: 0,\n        ref: l,\n        className: T(tt, Mt, r == null ? void 0 : r.toast, (yt = t == null ? void 0 : t.classNames) == null ? void 0 : yt.toast, r == null ? void 0 : r.default, r == null ? void 0 : r[x], (xt = t == null ? void 0 : t.classNames) == null ? void 0 : xt[x]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (vt = t.richColors) != null ? vt : Q,\n        \"data-styled\": !(t.jsx || t.unstyled || n),\n        \"data-mounted\": z,\n        \"data-promise\": !!t.promise,\n        \"data-removed\": D,\n        \"data-visible\": J,\n        \"data-y-position\": gt,\n        \"data-x-position\": zt,\n        \"data-index\": d,\n        \"data-front\": _,\n        \"data-swiping\": st,\n        \"data-dismissible\": P,\n        \"data-type\": x,\n        \"data-invert\": jt,\n        \"data-swipe-out\": M,\n        \"data-expanded\": !!($ || W && z),\n        style: {\n            \"--index\": d,\n            \"--toasts-before\": d,\n            \"--z-index\": q.length - d,\n            \"--offset\": \"\".concat(D ? c : Y.current, \"px\"),\n            \"--initial-height\": W ? \"auto\" : \"\".concat(y, \"px\"),\n            ...O,\n            ...t.style\n        },\n        onPointerDown: (a)=>{\n            lt || !P || (A.current = new Date, m(Y.current), a.target.setPointerCapture(a.pointerId), a.target.tagName !== \"BUTTON\" && (N(!0), F.current = {\n                x: a.clientX,\n                y: a.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var B, C, R, dt;\n            if (M || !P) return;\n            F.current = null;\n            let a = Number(((B = l.current) == null ? void 0 : B.style.getPropertyValue(\"--swipe-amount\").replace(\"px\", \"\")) || 0), f = new Date().getTime() - ((C = A.current) == null ? void 0 : C.getTime()), p = Math.abs(a) / f;\n            if (Math.abs(a) >= oe || p > .11) {\n                m(Y.current), (R = t.onDismiss) == null || R.call(t, t), L(), rt(!0);\n                return;\n            }\n            (dt = l.current) == null || dt.style.setProperty(\"--swipe-amount\", \"0px\"), N(!1);\n        },\n        onPointerMove: (a)=>{\n            var Bt;\n            if (!F.current || !P) return;\n            let f = a.clientY - F.current.y, p = a.clientX - F.current.x, C = (gt === \"top\" ? Math.min : Math.max)(0, f), R = a.pointerType === \"touch\" ? 10 : 2;\n            Math.abs(C) > R ? (Bt = l.current) == null || Bt.style.setProperty(\"--swipe-amount\", \"\".concat(f, \"px\")) : Math.abs(p) > R && (F.current = null);\n        }\n    }, Lt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": at,\n        \"data-disabled\": lt,\n        \"data-close-button\": !0,\n        onClick: lt || !P ? ()=>{} : ()=>{\n            var a;\n            L(), (a = t.onDismiss) == null || a.call(t, t);\n        },\n        className: T(r == null ? void 0 : r.closeButton, (wt = t == null ? void 0 : t.classNames) == null ? void 0 : wt.closeButton)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"18\",\n        y1: \"6\",\n        x2: \"6\",\n        y2: \"18\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"6\",\n        y1: \"6\",\n        x2: \"18\",\n        y2: \"18\"\n    }))) : null, t.jsx || /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.title) ? t.jsx || t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, x || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: T(r == null ? void 0 : r.icon, (Tt = t == null ? void 0 : t.classNames) == null ? void 0 : Tt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Yt() : null, t.type !== \"loading\" ? t.icon || (I == null ? void 0 : I[x]) || Ct(x) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: T(r == null ? void 0 : r.content, (St = t == null ? void 0 : t.classNames) == null ? void 0 : St.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: T(r == null ? void 0 : r.title, (Rt = t == null ? void 0 : t.classNames) == null ? void 0 : Rt.title)\n    }, t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: T(et, At, r == null ? void 0 : r.description, (Et = t == null ? void 0 : t.classNames) == null ? void 0 : Et.description)\n    }, t.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.cancel) ? t.cancel : t.cancel && U(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || K,\n        onClick: (a)=>{\n            var f, p;\n            U(t.cancel) && P && ((p = (f = t.cancel).onClick) == null || p.call(f, a), L());\n        },\n        className: T(r == null ? void 0 : r.cancelButton, (Nt = t == null ? void 0 : t.classNames) == null ? void 0 : Nt.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.action) ? t.action : t.action && U(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || Z,\n        onClick: (a)=>{\n            var f, p;\n            U(t.action) && (a.defaultPrevented || ((p = (f = t.action).onClick) == null || p.call(f, a), L()));\n        },\n        className: T(r == null ? void 0 : r.actionButton, (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt.actionButton)\n    }, t.action.label) : null));\n};\n_s1(se, \"WXe5GqkbpcbbcyIs4dOqGgiJxyo=\");\n_c2 = se;\nfunction Ht() {\n    if (typeof window == \"undefined\" || typeof document == \"undefined\") return \"ltr\";\n    let s = document.documentElement.getAttribute(\"dir\");\n    return s === \"auto\" || !s ? window.getComputedStyle(document.documentElement).direction : s;\n}\n_c3 = Ht;\nfunction we() {\n    _s2();\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"we.useEffect\": ()=>v.subscribe({\n                \"we.useEffect\": (t)=>{\n                    o({\n                        \"we.useEffect\": (n)=>{\n                            if (\"dismiss\" in t && t.dismiss) return n.filter({\n                                \"we.useEffect\": (u)=>u.id !== t.id\n                            }[\"we.useEffect\"]);\n                            let h = n.findIndex({\n                                \"we.useEffect.h\": (u)=>u.id === t.id\n                            }[\"we.useEffect.h\"]);\n                            if (h !== -1) {\n                                let u = [\n                                    ...n\n                                ];\n                                return u[h] = {\n                                    ...u[h],\n                                    ...t\n                                }, u;\n                            } else return [\n                                t,\n                                ...n\n                            ];\n                        }\n                    }[\"we.useEffect\"]);\n                }\n            }[\"we.useEffect\"])\n    }[\"we.useEffect\"], []), {\n        toasts: s\n    };\n}\n_s2(we, \"ma9aa7WSMRlCd84CR+pNoEUcuwI=\");\nvar Te = (s)=>{\n    _s3();\n    let { invert: o, position: t = \"bottom-right\", hotkey: n = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: h, closeButton: u, className: g, offset: b, theme: d = \"light\", richColors: q, duration: $, style: V, visibleToasts: Q = qt, toastOptions: i, dir: O = Ht(), gap: K = ee, loadingIcon: Z, icons: tt, containerAriaLabel: et = \"Notifications\", pauseWhenPageIsHidden: X, cn: ot = ne } = s, [w, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), W = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Te.useMemo[W]\": ()=>Array.from(new Set([\n                t\n            ].concat(w.filter({\n                \"Te.useMemo[W]\": (c)=>c.position\n            }[\"Te.useMemo[W]\"]).map({\n                \"Te.useMemo[W]\": (c)=>c.position\n            }[\"Te.useMemo[W]\"]))))\n    }[\"Te.useMemo[W]\"], [\n        w,\n        t\n    ]), [r, I] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [at, k] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [T, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [nt, D] = react__WEBPACK_IMPORTED_MODULE_0__.useState(d !== \"system\" ? d : typeof window != \"undefined\" && window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\"), H = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), st = n.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), N = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), M = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), rt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Te.useCallback[rt]\": (c)=>{\n            var m;\n            (m = w.find({\n                \"Te.useCallback[rt]\": (y)=>y.id === c.id\n            }[\"Te.useCallback[rt]\"])) != null && m.delete || v.dismiss(c.id), j({\n                \"Te.useCallback[rt]\": (y)=>y.filter({\n                        \"Te.useCallback[rt]\": (param)=>{\n                            let { id: S } = param;\n                            return S !== c.id;\n                        }\n                    }[\"Te.useCallback[rt]\"])\n            }[\"Te.useCallback[rt]\"]);\n        }\n    }[\"Te.useCallback[rt]\"], [\n        w\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>v.subscribe({\n                \"Te.useEffect\": (c)=>{\n                    if (c.dismiss) {\n                        j({\n                            \"Te.useEffect\": (m)=>m.map({\n                                    \"Te.useEffect\": (y)=>y.id === c.id ? {\n                                            ...y,\n                                            delete: !0\n                                        } : y\n                                }[\"Te.useEffect\"])\n                        }[\"Te.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"Te.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Te.useEffect\": ()=>{\n                                    j({\n                                        \"Te.useEffect\": (m)=>{\n                                            let y = m.findIndex({\n                                                \"Te.useEffect.y\": (S)=>S.id === c.id\n                                            }[\"Te.useEffect.y\"]);\n                                            return y !== -1 ? [\n                                                ...m.slice(0, y),\n                                                {\n                                                    ...m[y],\n                                                    ...c\n                                                },\n                                                ...m.slice(y + 1)\n                                            ] : [\n                                                c,\n                                                ...m\n                                            ];\n                                        }\n                                    }[\"Te.useEffect\"]);\n                                }\n                            }[\"Te.useEffect\"]);\n                        }\n                    }[\"Te.useEffect\"]);\n                }\n            }[\"Te.useEffect\"])\n    }[\"Te.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            if (d !== \"system\") {\n                D(d);\n                return;\n            }\n            d === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? D(\"dark\") : D(\"light\")), typeof window != \"undefined\" && window.matchMedia(\"(prefers-color-scheme: dark)\").addEventListener(\"change\", {\n                \"Te.useEffect\": (param)=>{\n                    let { matches: c } = param;\n                    D(c ? \"dark\" : \"light\");\n                }\n            }[\"Te.useEffect\"]);\n        }\n    }[\"Te.useEffect\"], [\n        d\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            w.length <= 1 && k(!1);\n        }\n    }[\"Te.useEffect\"], [\n        w\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            let c = {\n                \"Te.useEffect.c\": (m)=>{\n                    var S, A;\n                    n.every({\n                        \"Te.useEffect.c\": (l)=>m[l] || m.code === l\n                    }[\"Te.useEffect.c\"]) && (k(!0), (S = H.current) == null || S.focus()), m.code === \"Escape\" && (document.activeElement === H.current || (A = H.current) != null && A.contains(document.activeElement)) && k(!1);\n                }\n            }[\"Te.useEffect.c\"];\n            return document.addEventListener(\"keydown\", c), ({\n                \"Te.useEffect\": ()=>document.removeEventListener(\"keydown\", c)\n            })[\"Te.useEffect\"];\n        }\n    }[\"Te.useEffect\"], [\n        n\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            if (H.current) return ({\n                \"Te.useEffect\": ()=>{\n                    N.current && (N.current.focus({\n                        preventScroll: !0\n                    }), N.current = null, M.current = !1);\n                }\n            })[\"Te.useEffect\"];\n        }\n    }[\"Te.useEffect\"], [\n        H.current\n    ]), w.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        \"aria-label\": \"\".concat(et, \" \").concat(st),\n        tabIndex: -1\n    }, W.map((c, m)=>{\n        var A;\n        let [y, S] = c.split(\"-\");\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: c,\n            dir: O === \"auto\" ? Ht() : O,\n            tabIndex: -1,\n            ref: H,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": nt,\n            \"data-y-position\": y,\n            \"data-x-position\": S,\n            style: {\n                \"--front-toast-height\": \"\".concat(((A = r[0]) == null ? void 0 : A.height) || 0, \"px\"),\n                \"--offset\": typeof b == \"number\" ? \"\".concat(b, \"px\") : b || Qt,\n                \"--width\": \"\".concat(te, \"px\"),\n                \"--gap\": \"\".concat(K, \"px\"),\n                ...V\n            },\n            onBlur: (l)=>{\n                M.current && !l.currentTarget.contains(l.relatedTarget) && (M.current = !1, N.current && (N.current.focus({\n                    preventScroll: !0\n                }), N.current = null));\n            },\n            onFocus: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || M.current || (M.current = !0, N.current = l.relatedTarget);\n            },\n            onMouseEnter: ()=>k(!0),\n            onMouseMove: ()=>k(!0),\n            onMouseLeave: ()=>{\n                T || k(!1);\n            },\n            onPointerDown: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || z(!0);\n            },\n            onPointerUp: ()=>z(!1)\n        }, w.filter((l)=>!l.position && m === 0 || l.position === c).map((l, _)=>{\n            var J, x;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(se, {\n                key: l.id,\n                icons: tt,\n                index: _,\n                toast: l,\n                defaultRichColors: q,\n                duration: (J = i == null ? void 0 : i.duration) != null ? J : $,\n                className: i == null ? void 0 : i.className,\n                descriptionClassName: i == null ? void 0 : i.descriptionClassName,\n                invert: o,\n                visibleToasts: Q,\n                closeButton: (x = i == null ? void 0 : i.closeButton) != null ? x : u,\n                interacting: T,\n                position: c,\n                style: i == null ? void 0 : i.style,\n                unstyled: i == null ? void 0 : i.unstyled,\n                classNames: i == null ? void 0 : i.classNames,\n                cancelButtonStyle: i == null ? void 0 : i.cancelButtonStyle,\n                actionButtonStyle: i == null ? void 0 : i.actionButtonStyle,\n                removeToast: rt,\n                toasts: w.filter((P)=>P.position == l.position),\n                heights: r.filter((P)=>P.position == l.position),\n                setHeights: I,\n                expandByDefault: h,\n                gap: K,\n                loadingIcon: Z,\n                expanded: at,\n                pauseWhenPageIsHidden: X,\n                cn: ot\n            });\n        }));\n    })) : null;\n};\n_s3(Te, \"T0Qd0KT3Zq/2TB4TFPqVCWpWZmk=\");\n_c4 = Te;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Dt\");\n$RefreshReg$(_c1, \"U\");\n$RefreshReg$(_c2, \"se\");\n$RefreshReg$(_c3, \"Ht\");\n$RefreshReg$(_c4, \"Te\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ })

});