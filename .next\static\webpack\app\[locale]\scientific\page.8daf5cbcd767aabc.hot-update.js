"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/scientific/page",{

/***/ "(app-pages-browser)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ScientificCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const isRTL = locale === 'ar';\n    // Calculator state\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Helper functions\n    const toRadians = (degrees)=>degrees * (Math.PI / 180);\n    const toDegrees = (radians)=>radians * (180 / Math.PI);\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[addToHistory]\": (calculation)=>{\n            setHistory({\n                \"ScientificCalculator.useCallback[addToHistory]\": (prev)=>[\n                        calculation,\n                        ...prev.slice(0, 9)\n                    ]\n            }[\"ScientificCalculator.useCallback[addToHistory]\"]);\n        }\n    }[\"ScientificCalculator.useCallback[addToHistory]\"], []);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' && num !== '.' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performCalculation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performCalculation]\": ()=>{\n            const inputValue = parseFloat(display);\n            if (previousValue !== null && operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(null);\n                setOperation(null);\n                setWaitingForNewValue(true);\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n        }\n    }[\"ScientificCalculator.useCallback[performCalculation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const performFunction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performFunction]\": (func)=>{\n            const value = parseFloat(display);\n            let result;\n            switch(func){\n                case 'sin':\n                    result = Math.sin(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'cos':\n                    result = Math.cos(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'tan':\n                    result = Math.tan(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'ln':\n                    result = Math.log(value);\n                    break;\n                case 'log':\n                    result = Math.log10(value);\n                    break;\n                case 'sqrt':\n                    result = Math.sqrt(value);\n                    break;\n                case 'square':\n                    result = value * value;\n                    break;\n                case 'factorial':\n                    result = value <= 0 ? 1 : Array.from({\n                        length: value\n                    }, {\n                        \"ScientificCalculator.useCallback[performFunction]\": (_, i)=>i + 1\n                    }[\"ScientificCalculator.useCallback[performFunction]\"]).reduce({\n                        \"ScientificCalculator.useCallback[performFunction]\": (a, b)=>a * b\n                    }[\"ScientificCalculator.useCallback[performFunction]\"], 1);\n                    break;\n                case 'reciprocal':\n                    result = 1 / value;\n                    break;\n                default:\n                    result = value;\n            }\n            setDisplay(String(result));\n            setWaitingForNewValue(true);\n            addToHistory(\"\".concat(func, \"(\").concat(value, \") = \").concat(result));\n        }\n    }[\"ScientificCalculator.useCallback[performFunction]\"], [\n        display,\n        angleMode,\n        addToHistory\n    ]);\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'pi':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: t('calculator')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 p-8 rounded-lg text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: t('seo.intro')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-4\",\n                                                    children: \"Scientific calculator functionality will be implemented here.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScientificCalculator, \"EyLPQnZffevf5NtWITwAKx/Q6f0=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale\n    ];\n});\n_c = ScientificCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\nvar _c;\n$RefreshReg$(_c, \"ScientificCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ScientificCalculator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast(param) {\n    let { ...props } = param;\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-toast.ts\n"));

/***/ })

});