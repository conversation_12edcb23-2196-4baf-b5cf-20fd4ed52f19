const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin('./i18n/request.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  },
  images: {
    domains: [],
  },
  async redirects() {
    return [
      {
        source: '/scientific',
        destination: '/en/scientific',
        permanent: true,
      },
      {
        source: '/financial',
        destination: '/en/financial',
        permanent: true,
      },
      {
        source: '/conversion',
        destination: '/en/conversion',
        permanent: true,
      },
      {
        source: '/health',
        destination: '/en/health',
        permanent: true,
      },
    ];
  },
};

module.exports = withNextIntl(nextConfig);
