
import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'en' | 'fr' | 'es' | 'ar';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
  translations: Record<string, string>;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [translations, setTranslations] = useState<Record<string, string>>({});

  const isRTL = language === 'ar';

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('preferred-language', lang);
    
    // Update HTML attributes for RTL support
    if (lang === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.setAttribute('lang', 'ar');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.setAttribute('lang', lang);
    }
  };

  // Load translations dynamically
  useEffect(() => {
    const loadTranslations = async () => {
      try {
        const translationModule = await import(`../translations/${language}.ts`);
        setTranslations(translationModule.default);
      } catch (error) {
        console.error(`Failed to load translations for ${language}:`, error);
        // Fallback to English
        if (language !== 'en') {
          try {
            const fallbackModule = await import('../translations/en.ts');
            setTranslations(fallbackModule.default);
          } catch (fallbackError) {
            console.error('Failed to load fallback translations:', fallbackError);
          }
        }
      }
    };

    loadTranslations();
  }, [language]);

  // Initialize language from URL or localStorage
  useEffect(() => {
    const detectLanguageFromPath = () => {
      const path = window.location.pathname;
      
      if (path.startsWith('/fr/') || path === '/fr') {
        return 'fr';
      } else if (path.startsWith('/es/') || path === '/es') {
        return 'es';
      } else if (path.startsWith('/ar/') || path === '/ar') {
        return 'ar';
      }
      return 'en';
    };

    const urlLanguage = detectLanguageFromPath();
    const savedLanguage = localStorage.getItem('preferred-language') as Language;
    
    // Priority: URL language > saved language > default (en)
    const initialLanguage = urlLanguage !== 'en' ? urlLanguage : (savedLanguage || 'en');
    
    setLanguage(initialLanguage);
  }, []);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL, translations }}>
      {children}
    </LanguageContext.Provider>
  );
};
