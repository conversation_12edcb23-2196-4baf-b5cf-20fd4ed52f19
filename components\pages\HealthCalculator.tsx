'use client';

import React, { useState } from 'react';
import { Heart, Calculator, Scale, Activity, Target, Users, BookOpen, Shield, Zap } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

const HealthCalculator = () => {
  const t = useTranslations('health');
  const locale = useLocale();
  const isRTL = locale === 'ar';

  // BMI State
  const [bmiWeight, setBmiWeight] = useState('');
  const [bmiHeight, setBmiHeight] = useState('');
  const [bmiResult, setBmiResult] = useState<{
    bmi: number;
    category: string;
    color: string;
  } | null>(null);

  // BMR State
  const [bmrWeight, setBmrWeight] = useState('');
  const [bmrHeight, setBmrHeight] = useState('');
  const [bmrAge, setBmrAge] = useState('');
  const [bmrGender, setBmrGender] = useState('');
  const [bmrResult, setBmrResult] = useState<number | null>(null);

  // Calorie State
  const [calorieWeight, setCalorieWeight] = useState('');
  const [calorieHeight, setCalorieHeight] = useState('');
  const [calorieAge, setCalorieAge] = useState('');
  const [calorieGender, setCalorieGender] = useState('');
  const [activityLevel, setActivityLevel] = useState('');
  const [calorieResult, setCalorieResult] = useState<{
    bmr: number;
    maintenance: number;
    loseWeight: number;
    gainWeight: number;
  } | null>(null);

  const [activeTab, setActiveTab] = useState('bmi');

  const calculateBMI = () => {
    const weight = parseFloat(bmiWeight);
    const height = parseFloat(bmiHeight) / 100; // Convert cm to m

    if (weight && height) {
      const bmi = weight / (height * height);
      let category = '';
      let color = '';

      if (bmi < 18.5) {
        category = t('underweight') || 'Underweight';
        color = 'text-blue-600';
      } else if (bmi < 25) {
        category = t('normal') || 'Normal';
        color = 'text-green-600';
      } else if (bmi < 30) {
        category = t('overweight') || 'Overweight';
        color = 'text-yellow-600';
      } else {
        category = t('obese') || 'Obese';
        color = 'text-red-600';
      }

      setBmiResult({ bmi, category, color });
    }
  };

  const calculateBMR = () => {
    const weight = parseFloat(bmrWeight);
    const height = parseFloat(bmrHeight);
    const age = parseFloat(bmrAge);

    if (weight && height && age && bmrGender) {
      let bmr: number;
      
      if (bmrGender === 'male') {
        // Mifflin-St Jeor Equation for men
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        // Mifflin-St Jeor Equation for women
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }

      setBmrResult(bmr);
    }
  };

  const calculateCalories = () => {
    const weight = parseFloat(calorieWeight);
    const height = parseFloat(calorieHeight);
    const age = parseFloat(calorieAge);

    if (weight && height && age && calorieGender && activityLevel) {
      let bmr: number;
      
      if (calorieGender === 'male') {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }

      const activityMultipliers = {
        'sedentary': 1.2,
        'light': 1.375,
        'moderate': 1.55,
        'active': 1.725,
        'extremely_active': 1.9
      };

      const multiplier = activityMultipliers[activityLevel as keyof typeof activityMultipliers];
      const maintenance = bmr * multiplier;
      const loseWeight = maintenance - 500;
      const gainWeight = maintenance + 500;

      setCalorieResult({
        bmr,
        maintenance,
        loseWeight,
        gainWeight
      });
    }
  };

  const getBMIProgressWidth = (bmi: number) => {
    if (bmi < 18.5) return Math.min((bmi / 18.5) * 25, 25);
    if (bmi < 25) return 25 + ((bmi - 18.5) / (25 - 18.5)) * 25;
    if (bmi < 30) return 50 + ((bmi - 25) / (30 - 25)) * 25;
    return Math.min(75 + ((bmi - 30) / 10) * 25, 100);
  };

  const tabsConfig = [
    { value: 'bmi', icon: Heart, label: t('bmi') || 'BMI' },
    { value: 'bmr', icon: Activity, label: t('bmr') || 'BMR' },
    { value: 'calories', icon: Calculator, label: t('calories') || 'Calories' },
  ];

  const displayedTabs = isRTL ? [...tabsConfig].reverse() : tabsConfig;

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-rose-600 mx-auto rounded-full" />
        </div>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm mb-8">
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3">
              <Heart className="h-8 w-8 text-pink-600" />
              {t('title')}
            </CardTitle>
            <CardDescription className="text-lg text-gray-600">
              Calculate BMI, BMR, and daily calorie requirements for optimal health
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className={`grid w-full grid-cols-3 mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {displayedTabs.map((tab) => (
                  <TabsTrigger key={tab.value} value={tab.value} className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <tab.icon className="h-4 w-4" />
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* BMI Calculator */}
              <TabsContent value="bmi">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Heart className="h-5 w-5 text-pink-600" />
                        {t('bmi')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="bmiWeight" className={isRTL ? 'text-right block' : ''}>{t('weight') || 'Weight (kg)'}</Label>
                          <Input
                            id="bmiWeight"
                            type="number"
                            value={bmiWeight}
                            onChange={(e) => setBmiWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmiHeight" className={isRTL ? 'text-right block' : ''}>{t('height') || 'Height (cm)'}</Label>
                          <Input
                            id="bmiHeight"
                            type="number"
                            value={bmiHeight}
                            onChange={(e) => setBmiHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                      </div>
                      <Button onClick={calculateBMI} className="w-full bg-pink-600 hover:bg-pink-700">
                        Calculate
                      </Button>
                    </CardContent>
                  </Card>

                  {bmiResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>BMI Result</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="text-center">
                          <div className="text-4xl font-bold text-pink-600 mb-2">
                            {bmiResult.bmi.toFixed(1)}
                          </div>
                          <div className={`text-lg font-semibold ${bmiResult.color}`}>
                            {bmiResult.category}
                          </div>
                        </div>
                        
                        {/* BMI Scale */}
                        <div className="space-y-2">
                          <div className={`text-sm text-gray-600 ${isRTL ? 'text-right' : ''}`}>BMI Scale</div>
                          <div className="relative h-4 bg-gradient-to-r from-blue-400 via-green-400 via-yellow-400 to-red-400 rounded-full">
                            <div 
                              className="absolute top-0 w-2 h-4 bg-gray-800 rounded-full transform -translate-x-1"
                              style={{ left: `${getBMIProgressWidth(bmiResult.bmi)}%` }}
                            />
                          </div>
                          <div className={`flex justify-between text-xs text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>Underweight</span>
                            <span>Normal</span>
                            <span>Overweight</span>
                            <span>Obese</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="p-2 bg-blue-50 rounded">
                            <div className={`text-blue-600 font-medium ${isRTL ? 'text-right' : ''}`}>Underweight</div>
                            <div className={`text-blue-800 ${isRTL ? 'text-right' : ''}`}>&lt; 18.5</div>
                          </div>
                          <div className="p-2 bg-green-50 rounded">
                            <div className={`text-green-600 font-medium ${isRTL ? 'text-right' : ''}`}>Normal</div>
                            <div className={`text-green-800 ${isRTL ? 'text-right' : ''}`}>18.5 - 24.9</div>
                          </div>
                          <div className="p-2 bg-yellow-50 rounded">
                            <div className={`text-yellow-600 font-medium ${isRTL ? 'text-right' : ''}`}>Overweight</div>
                            <div className={`text-yellow-800 ${isRTL ? 'text-right' : ''}`}>25 - 29.9</div>
                          </div>
                          <div className="p-2 bg-red-50 rounded">
                            <div className={`text-red-600 font-medium ${isRTL ? 'text-right' : ''}`}>Obese</div>
                            <div className={`text-red-800 ${isRTL ? 'text-right' : ''}`}>≥ 30</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* BMR Calculator */}
              <TabsContent value="bmr">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Activity className="h-5 w-5 text-blue-600" />
                        {t('bmr')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="bmrWeight" className={isRTL ? 'text-right block' : ''}>{t('weight') || 'Weight (kg)'}</Label>
                          <Input
                            id="bmrWeight"
                            type="number"
                            value={bmrWeight}
                            onChange={(e) => setBmrWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmrHeight" className={isRTL ? 'text-right block' : ''}>{t('height') || 'Height (cm)'}</Label>
                          <Input
                            id="bmrHeight"
                            type="number"
                            value={bmrHeight}
                            onChange={(e) => setBmrHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmrAge" className={isRTL ? 'text-right block' : ''}>{t('age') || 'Age'}</Label>
                          <Input
                            id="bmrAge"
                            type="number"
                            value={bmrAge}
                            onChange={(e) => setBmrAge(e.target.value)}
                            placeholder="30"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{t('gender') || 'Gender'}</Label>
                          <Select value={bmrGender} onValueChange={setBmrGender}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={t('gender') || 'Select Gender'} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">{t('male') || 'Male'}</SelectItem>
                              <SelectItem value="female">{t('female') || 'Female'}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <Button onClick={calculateBMR} className="w-full bg-blue-600 hover:bg-blue-700">
                        Calculate
                      </Button>
                    </CardContent>
                  </Card>

                  {bmrResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>BMR Result</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center">
                          <div className="text-4xl font-bold text-blue-600 mb-2">
                            {Math.round(bmrResult)}
                          </div>
                          <div className="text-lg text-gray-600">Calories per day</div>
                          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                            <p className={`text-sm text-gray-700 ${isRTL ? 'text-right' : ''}`}>
                              This is the number of calories your body needs at rest to maintain basic physiological functions.
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Calorie Calculator */}
              <TabsContent value="calories">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Calculator className="h-5 w-5 text-green-600" />
                        {t('calories')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="calorieWeight" className={isRTL ? 'text-right block' : ''}>{t('weight') || 'Weight (kg)'}</Label>
                          <Input
                            id="calorieWeight"
                            type="number"
                            value={calorieWeight}
                            onChange={(e) => setCalorieWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="calorieHeight" className={isRTL ? 'text-right block' : ''}>{t('height') || 'Height (cm)'}</Label>
                          <Input
                            id="calorieHeight"
                            type="number"
                            value={calorieHeight}
                            onChange={(e) => setCalorieHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="calorieAge" className={isRTL ? 'text-right block' : ''}>{t('age') || 'Age'}</Label>
                          <Input
                            id="calorieAge"
                            type="number"
                            value={calorieAge}
                            onChange={(e) => setCalorieAge(e.target.value)}
                            placeholder="30"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{t('gender') || 'Gender'}</Label>
                          <Select value={calorieGender} onValueChange={setCalorieGender}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={t('gender') || 'Select Gender'} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">{t('male') || 'Male'}</SelectItem>
                              <SelectItem value="female">{t('female') || 'Female'}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{t('activity_level') || 'Activity Level'}</Label>
                          <Select value={activityLevel} onValueChange={setActivityLevel}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={t('activity_level') || 'Select Activity Level'} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="sedentary">{t('sedentary') || 'Sedentary (little/no exercise)'}</SelectItem>
                              <SelectItem value="light">{t('light') || 'Light (light exercise 1-3 days/week)'}</SelectItem>
                              <SelectItem value="moderate">{t('moderate') || 'Moderate (moderate exercise 3-5 days/week)'}</SelectItem>
                              <SelectItem value="active">{t('active') || 'Active (hard exercise 6-7 days/week)'}</SelectItem>
                              <SelectItem value="extremely_active">{t('extremely_active') || 'Very Active (very hard exercise, physical job)'}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <Button onClick={calculateCalories} className="w-full bg-green-600 hover:bg-green-700">
                        Calculate
                      </Button>
                    </CardContent>
                  </Card>

                  {calorieResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>Calorie Results</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <div className={`text-sm text-blue-600 ${isRTL ? 'text-right' : ''}`}>BMR</div>
                            <div className={`text-2xl font-bold text-blue-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.bmr)}
                            </div>
                            <div className={`text-xs text-blue-600 ${isRTL ? 'text-right' : ''}`}>Calories at rest</div>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <div className={`text-sm text-green-600 ${isRTL ? 'text-right' : ''}`}>Maintenance</div>
                            <div className={`text-2xl font-bold text-green-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.maintenance)}
                            </div>
                            <div className={`text-xs text-green-600 ${isRTL ? 'text-right' : ''}`}>Calories to maintain weight</div>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <div className={`text-sm text-orange-600 ${isRTL ? 'text-right' : ''}`}>Weight Loss</div>
                            <div className={`text-2xl font-bold text-orange-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.loseWeight)}
                            </div>
                            <div className={`text-xs text-orange-600 ${isRTL ? 'text-right' : ''}`}>Calories to lose 1 lb/week</div>
                          </div>
                          <div className="p-4 bg-purple-50 rounded-lg">
                            <div className={`text-sm text-purple-600 ${isRTL ? 'text-right' : ''}`}>Weight Gain</div>
                            <div className={`text-2xl font-bold text-purple-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.gainWeight)}
                            </div>
                            <div className={`text-xs text-purple-600 ${isRTL ? 'text-right' : ''}`}>Calories to gain 1 lb/week</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>
            </Tabs>

            {/* Medical Disclaimer */}
            <Alert className="mt-8 border-pink-200 bg-pink-50">
              <Shield className="h-4 w-4 text-pink-600" />
              <AlertDescription className="text-pink-800">
                {t('seo.disclaimer') || 'These calculations are for informational purposes only and should not replace professional medical advice.'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* SEO Content Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {t('seo.intro') || 'Professional health calculators for BMI, BMR, and daily calorie requirements. Get accurate health metrics to support your wellness journey.'}
            </p>
          </div>

          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Health Tools
              </h3>
              <div className="space-y-4">
                {[
                  { icon: Heart, text: 'BMI Calculator - Body Mass Index assessment' },
                  { icon: Activity, text: 'BMR Calculator - Basal Metabolic Rate calculation' },
                  { icon: Calculator, text: 'Calorie Calculator - Daily calorie requirements' },
                  { icon: Target, text: 'Weight Management - Loss and gain targets' }
                ].map((tool, index) => {
                  const Icon = tool.icon;
                  return (
                    <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className="h-6 w-6 text-pink-600 mt-1 flex-shrink-0" />
                      <p className="text-gray-700">{tool.text}</p>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="bg-gradient-to-br from-pink-50 to-rose-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <BookOpen className="h-6 w-6 text-pink-600" />
                Professional Applications
              </h3>
              <p className="text-gray-700 mb-4">
                Used by healthcare professionals, fitness trainers, and individuals for accurate health assessments and wellness planning.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Healthcare</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Fitness</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Nutrition</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Wellness</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthCalculator;
