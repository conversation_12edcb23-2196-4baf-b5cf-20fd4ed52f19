import React, { useState, useCallback, useEffect } from 'react';
import { Calculator, History, Settings, HardDrive, Cpu, Zap, BookOpen, TrendingUp, Users, Target, Copy, RotateCcw, Archive, Trash2 } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Badge } from '../components/ui/badge';
import { toast } from 'sonner';

const ScientificCalculator = () => {
  const { translations, isRTL } = useLanguage();
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<string | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForNewValue, setWaitingForNewValue] = useState(false);
  const [memory, setMemory] = useState(0);
  const [history, setHistory] = useState<string[]>([]);
  const [angleMode, setAngleMode] = useState<'deg' | 'rad'>('deg');

  // Keyboard support
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;
      
      if (key >= '0' && key <= '9') {
        inputNumber(key);
      } else if (key === '.') {
        inputNumber('.');
      } else if (key === '+') {
        inputOperation('+');
      } else if (key === '-') {
        inputOperation('-');
      } else if (key === '*') {
        inputOperation('×');
      } else if (key === '/') {
        event.preventDefault();
        inputOperation('÷');
      } else if (key === 'Enter' || key === '=') {
        performCalculation();
      } else if (key === 'Escape' || key === 'c' || key === 'C') {
        clear();
      } else if (key === 'Backspace') {
        setDisplay(display.slice(0, -1) || '0');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [display]);

  const inputNumber = useCallback((num: string) => {
    if (waitingForNewValue) {
      setDisplay(num);
      setWaitingForNewValue(false);
    } else {
      if (num === '.' && display.includes('.')) return;
      setDisplay(display === '0' ? num : display + num);
    }
  }, [display, waitingForNewValue]);

  const inputOperation = useCallback((nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(display);
    } else if (operation) {
      const currentValue = previousValue || '0';
      const newValue = calculate(parseFloat(currentValue), inputValue, operation);
      
      const historyEntry = `${currentValue} ${operation} ${inputValue} = ${newValue}`;
      setHistory(prev => [historyEntry, ...prev.slice(0, 19)]);
      
      setDisplay(String(newValue));
      setPreviousValue(String(newValue));
    }

    setWaitingForNewValue(true);
    setOperation(nextOperation);
  }, [display, previousValue, operation]);

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+': return firstValue + secondValue;
      case '-': return firstValue - secondValue;
      case '×': return firstValue * secondValue;
      case '÷': return secondValue !== 0 ? firstValue / secondValue : NaN;
      case '^': return Math.pow(firstValue, secondValue);
      case 'mod': return firstValue % secondValue;
      case 'xroot': return Math.pow(secondValue, 1 / firstValue);
      default: return secondValue;
    }
  };

  const performUnaryOperation = useCallback((op: string) => {
    const currentValue = parseFloat(display);
    let result: number;

    const angleMultiplier = angleMode === 'deg' ? Math.PI / 180 : 1;

    switch (op) {
      case '√': result = Math.sqrt(currentValue); break;
      case '∛': result = Math.cbrt(currentValue); break;
      case 'x²': result = Math.pow(currentValue, 2); break;
      case 'x³': result = Math.pow(currentValue, 3); break;
      case '1/x': result = currentValue !== 0 ? 1 / currentValue : NaN; break;
      case 'sin': result = Math.sin(currentValue * angleMultiplier); break;
      case 'cos': result = Math.cos(currentValue * angleMultiplier); break;
      case 'tan': result = Math.tan(currentValue * angleMultiplier); break;
      case 'asin': result = Math.asin(currentValue) / angleMultiplier; break;
      case 'acos': result = Math.acos(currentValue) / angleMultiplier; break;
      case 'atan': result = Math.atan(currentValue) / angleMultiplier; break;
      case 'sinh': result = Math.sinh(currentValue); break;
      case 'cosh': result = Math.cosh(currentValue); break;
      case 'tanh': result = Math.tanh(currentValue); break;
      case 'ln': result = Math.log(currentValue); break;
      case 'log': result = Math.log10(currentValue); break;
      case 'log2': result = Math.log2(currentValue); break;
      case 'exp': result = Math.exp(currentValue); break;
      case '10^x': result = Math.pow(10, currentValue); break;
      case '2^x': result = Math.pow(2, currentValue); break;
      case '!': result = factorial(currentValue); break;
      case '+/-': result = -currentValue; break;
      case 'abs': result = Math.abs(currentValue); break;
      case 'floor': result = Math.floor(currentValue); break;
      case 'ceil': result = Math.ceil(currentValue); break;
      case 'round': result = Math.round(currentValue); break;
      default: return;
    }

    const historyEntry = `${op}(${currentValue}) = ${result}`;
    setHistory(prev => [historyEntry, ...prev.slice(0, 19)]);
    
    setDisplay(isNaN(result) ? translations['scientific.error'] || 'Error' : String(result));
    setWaitingForNewValue(true);
  }, [display, angleMode, translations]);

  const factorial = (n: number): number => {
    if (n < 0 || !Number.isInteger(n) || n > 170) return NaN;
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  };

  const inputConstant = useCallback((constant: string) => {
    let value: number;
    switch (constant) {
      case 'π': value = Math.PI; break;
      case 'e': value = Math.E; break;
      case 'φ': value = (1 + Math.sqrt(5)) / 2; break;
      case '√2': value = Math.SQRT2; break;
      case '√π': value = Math.sqrt(Math.PI); break;
      case 'γ': value = 0.5772156649015329; break; // Euler-Mascheroni constant
      default: return;
    }
    setDisplay(String(value));
    setWaitingForNewValue(true);
  }, []);

  const performCalculation = useCallback(() => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const currentValue = parseFloat(previousValue);
      const newValue = calculate(currentValue, inputValue, operation);
      
      const historyEntry = `${previousValue} ${operation} ${inputValue} = ${newValue}`;
      setHistory(prev => [historyEntry, ...prev.slice(0, 19)]);
      
      setDisplay(isNaN(newValue) ? translations['scientific.error'] || 'Error' : String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForNewValue(true);
    }
  }, [display, previousValue, operation, translations]);

  const clear = useCallback(() => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForNewValue(false);
  }, []);

  const clearEntry = useCallback(() => {
    setDisplay('0');
    setWaitingForNewValue(false);
  }, []);

  const clearHistory = useCallback(() => {
    setHistory([]);
    toast(translations['scientific.history_cleared'], {
      description: translations['scientific.history_cleared_desc'],
    });
  }, [translations]);

  const copyResult = useCallback(() => {
    navigator.clipboard.writeText(display);
    toast(translations['scientific.copied'], {
      description: translations['scientific.copied_desc'],
    });
  }, [display, translations]);

  const memoryStore = useCallback(() => {
    setMemory(parseFloat(display));
    toast(translations['scientific.memory_stored'], {
      description: (translations['scientific.memory_stored_desc'] || '').replace('{value}', display),
    });
  }, [display, translations]);

  const memoryRecall = useCallback(() => {
    setDisplay(String(memory));
    setWaitingForNewValue(true);
  }, [memory]);

  const memoryClear = useCallback(() => {
    setMemory(0);
    toast(translations['scientific.memory_cleared'], {
      description: translations['scientific.memory_cleared_desc'],
    });
  }, [translations]);

  const memoryAdd = useCallback(() => {
    setMemory(memory + parseFloat(display));
    toast(translations['scientific.memory_added'], {
      description: (translations['scientific.memory_added_desc'] || '').replace('{value}', display),
    });
  }, [memory, display, translations]);

  const memorySubtract = useCallback(() => {
    setMemory(memory - parseFloat(display));
    toast(translations['scientific.memory_subtracted'], {
      description: (translations['scientific.memory_subtracted_desc'] || '').replace('{value}', display),
    });
  }, [memory, display, translations]);

  const BasicButtons = () => (
    <div className="grid grid-cols-4 gap-3">
      <Button variant="outline" onClick={clearEntry} className="bg-red-50 hover:bg-red-100 text-red-700">CE</Button>
      <Button variant="outline" onClick={clear} className="bg-red-50 hover:bg-red-100 text-red-700">C</Button>
      <Button variant="outline" onClick={() => setDisplay(display.slice(0, -1) || '0')}>⌫</Button>
      <Button variant="outline" onClick={() => inputOperation('÷')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">÷</Button>
      
      <Button variant="outline" onClick={() => inputNumber('7')}>7</Button>
      <Button variant="outline" onClick={() => inputNumber('8')}>8</Button>
      <Button variant="outline" onClick={() => inputNumber('9')}>9</Button>
      <Button variant="outline" onClick={() => inputOperation('×')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">×</Button>
      
      <Button variant="outline" onClick={() => inputNumber('4')}>4</Button>
      <Button variant="outline" onClick={() => inputNumber('5')}>5</Button>
      <Button variant="outline" onClick={() => inputNumber('6')}>6</Button>
      <Button variant="outline" onClick={() => inputOperation('-')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">-</Button>
      
      <Button variant="outline" onClick={() => inputNumber('1')}>1</Button>
      <Button variant="outline" onClick={() => inputNumber('2')}>2</Button>
      <Button variant="outline" onClick={() => inputNumber('3')}>3</Button>
      <Button variant="outline" onClick={() => inputOperation('+')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">+</Button>
      
      <Button variant="outline" onClick={() => performUnaryOperation('+/-')}>+/-</Button>
      <Button variant="outline" onClick={() => inputNumber('0')}>0</Button>
      <Button variant="outline" onClick={() => inputNumber('.')}>.</Button>
      <Button onClick={performCalculation} className="bg-calculator-primary hover:bg-calculator-primary/90">=</Button>
    </div>
  );

  const AdvancedButtons = () => (
    <div className="grid grid-cols-4 gap-3">
      <Button variant="outline" onClick={() => performUnaryOperation('x²')} className="bg-green-50 hover:bg-green-100 text-green-700">x²</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('x³')} className="bg-green-50 hover:bg-green-100 text-green-700">x³</Button>
      <Button variant="outline" onClick={() => inputOperation('^')} className="bg-green-50 hover:bg-green-100 text-green-700">xʸ</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('√')} className="bg-green-50 hover:bg-green-100 text-green-700">√</Button>
      
      <Button variant="outline" onClick={() => performUnaryOperation('∛')} className="bg-green-50 hover:bg-green-100 text-green-700">∛</Button>
      <Button variant="outline" onClick={() => inputOperation('xroot')} className="bg-green-50 hover:bg-green-100 text-green-700">ʸ√x</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('1/x')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">1/x</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('!')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">n!</Button>
      
      <Button variant="outline" onClick={() => inputOperation('mod')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">mod</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('abs')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">|x|</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('floor')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">⌊x⌋</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('ceil')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">⌈x⌉</Button>

      <Button variant="outline" onClick={() => performUnaryOperation('ln')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">ln</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('log')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">log</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('exp')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">eˣ</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('10^x')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">10ˣ</Button>
    </div>
  );

  const FunctionButtons = () => (
    <div className="grid grid-cols-4 gap-3">
      <Button variant="outline" onClick={() => performUnaryOperation('sin')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">sin</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('cos')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">cos</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('tan')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">tan</Button>
      <Button 
        variant="outline" 
        onClick={() => setAngleMode(angleMode === 'deg' ? 'rad' : 'deg')}
        className={`${angleMode === 'deg' ? 'bg-blue-100 text-blue-700' : 'bg-red-100 text-red-700'}`}
      >
        {angleMode.toUpperCase()}
      </Button>
      
      <Button variant="outline" onClick={() => performUnaryOperation('asin')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">sin⁻¹</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('acos')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">cos⁻¹</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('atan')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">tan⁻¹</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('2^x')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">2ˣ</Button>
      
      <Button variant="outline" onClick={() => performUnaryOperation('sinh')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">sinh</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('cosh')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">cosh</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('tanh')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">tanh</Button>
      <Button variant="outline" onClick={() => performUnaryOperation('log2')} className="bg-orange-50 hover:bg-orange-100 text-orange-700">log₂</Button>
    </div>
  );

  const ConstantButtons = () => (
    <div className="grid grid-cols-3 gap-3">
      <Button variant="outline" onClick={() => inputConstant('π')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">π</Button>
      <Button variant="outline" onClick={() => inputConstant('e')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">e</Button>
      <Button variant="outline" onClick={() => inputConstant('φ')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">φ</Button>
      
      <Button variant="outline" onClick={() => inputConstant('√2')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">√2</Button>
      <Button variant="outline" onClick={() => inputConstant('√π')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">√π</Button>
      <Button variant="outline" onClick={() => inputConstant('γ')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">γ</Button>
    </div>
  );

  const MemoryButtons = () => (
    <div className="space-y-3">
      <div className="grid grid-cols-3 gap-3">
        <Button variant="outline" onClick={memoryStore} className="bg-cyan-50 hover:bg-cyan-100 text-cyan-700">MS</Button>
        <Button variant="outline" onClick={memoryRecall} className="bg-cyan-50 hover:bg-cyan-100 text-cyan-700">MR</Button>
        <Button variant="outline" onClick={memoryClear} className="bg-cyan-50 hover:bg-cyan-100 text-cyan-700">MC</Button>
      </div>
      <div className="grid grid-cols-2 gap-3">
        <Button variant="outline" onClick={memoryAdd} className="bg-cyan-50 hover:bg-cyan-100 text-cyan-700">M+</Button>
        <Button variant="outline" onClick={memorySubtract} className="bg-cyan-50 hover:bg-cyan-100 text-cyan-700">M-</Button>
      </div>
      <div className="p-4 bg-gray-50 rounded-lg">
        <div className="text-sm text-gray-600">Memory: {memory}</div>
      </div>
    </div>
  );

  const tabItems = [
    { value: 'basic', label: translations['scientific.basic'] },
    { value: 'advanced', label: translations['scientific.advanced'] },
    { value: 'functions', label: translations['scientific.functions'] },
    { value: 'constants', label: translations['scientific.constants'] },
    { value: 'memory', label: translations['scientific.memory'] },
  ];

  const orderedTabs = isRTL ? [...tabItems].reverse() : tabItems;

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Main Calculator Interface */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {translations['scientific.title']}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-calculator-primary to-calculator-secondary mx-auto rounded-full" />
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
          {/* Calculator */}
          <div className="lg:col-span-2">
            <Card className="shadow-xl">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{translations['scientific.calculator']}</CardTitle>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={copyResult}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={clear}>
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Display */}
                <div className="bg-gray-900 text-white p-6 rounded-lg">
                  <div className="text-right text-3xl font-mono overflow-hidden" dir="ltr">
                    {display}
                  </div>
                  {operation && previousValue && (
                    <div className="text-right text-sm text-gray-400 mt-2" dir={isRTL ? 'rtl' : 'ltr'}>
                      {isRTL ? `${operation} ${previousValue}` : `${previousValue} ${operation}`}
                    </div>
                  )}
                  <div className="text-right text-xs text-gray-500 mt-1">
                    {(translations['scientific.angle_mode'] || 'Angle: {mode}').replace('{mode}', angleMode.toUpperCase())}
                  </div>
                </div>

                {/* Button Tabs */}
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-5">
                    {orderedTabs.map((tab) => (
                      <TabsTrigger key={tab.value} value={tab.value}>
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  
                  <TabsContent value="basic" className="mt-6">
                    <BasicButtons />
                  </TabsContent>
                  
                  <TabsContent value="advanced" className="mt-6">
                    <AdvancedButtons />
                  </TabsContent>
                  
                  <TabsContent value="functions" className="mt-6">
                    <FunctionButtons />
                  </TabsContent>
                  
                  <TabsContent value="constants" className="mt-6">
                    <ConstantButtons />
                  </TabsContent>
                  
                  <TabsContent value="memory" className="mt-6">
                    <MemoryButtons />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* History */}
          <div>
            <Card className="shadow-xl">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Archive className="h-5 w-5" />
                    {translations['scientific.history']}
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={clearHistory}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {history.length === 0 ? (
                    <p className="text-gray-500 text-sm">{translations['scientific.no_history']}</p>
                  ) : (
                    history.map((entry, index) => (
                      <div
                        key={index}
                        className="p-3 bg-gray-50 rounded-lg text-sm font-mono cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => {
                          const result = entry.split(' = ')[1];
                          if (result && result !== (translations['scientific.error'] || 'Error')) {
                            setDisplay(result);
                            setWaitingForNewValue(true);
                          }
                        }}
                        dir="ltr"
                      >
                        {entry}
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* SEO Content Section - Moved below the calculator */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {translations['scientific.seo.intro']}
            </p>
          </div>
          
          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {translations['scientific.seo.features_title']}
              </h3>
              <div className="space-y-4">
                {[
                  { icon: Cpu, key: 'scientific.seo.feature1' },
                  { icon: TrendingUp, key: 'scientific.seo.feature2' },
                  { icon: HardDrive, key: 'scientific.seo.feature3' },
                  { icon: History, key: 'scientific.seo.feature4' },
                  { icon: Target, key: 'scientific.seo.feature5' }
                ].map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className="h-6 w-6 text-calculator-primary mt-1 flex-shrink-0" />
                      <p className="text-gray-700">{translations[feature.key]}</p>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-calculator-primary/5 to-calculator-secondary/5 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <BookOpen className="h-6 w-6 text-calculator-primary" />
                Use Cases
              </h3>
              <p className="text-gray-700 mb-4">
                {translations['scientific.seo.use_cases']}
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Physics</Badge>
                <Badge variant="secondary">Engineering</Badge>
                <Badge variant="secondary">Mathematics</Badge>
                <Badge variant="secondary">Research</Badge>
                <Badge variant="secondary">Education</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScientificCalculator;
