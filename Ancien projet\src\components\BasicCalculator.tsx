
import React, { useState, useCallback } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Copy } from 'lucide-react';
import { toast } from '../hooks/use-toast';
import { useLanguage } from '../contexts/LanguageContext';

const BasicCalculator = () => {
  const { translations, isRTL } = useLanguage();
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<string | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForNewValue, setWaitingForNewValue] = useState(false);

  const inputNumber = useCallback((num: string) => {
    if (waitingForNewValue) {
      setDisplay(num);
      setWaitingForNewValue(false);
    } else {
      if (num === '.' && display.includes('.')) return;
      setDisplay(display === '0' && num !== '.' ? num : display + num);
    }
  }, [display, waitingForNewValue]);

  const inputOperation = useCallback((nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(display);
    } else if (operation) {
      const currentValue = previousValue || '0';
      const newValue = calculate(parseFloat(currentValue), inputValue, operation);
      
      setDisplay(String(newValue));
      setPreviousValue(String(newValue));
    }

    setWaitingForNewValue(true);
    setOperation(nextOperation);
  }, [display, previousValue, operation]);

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+': return firstValue + secondValue;
      case '-': return firstValue - secondValue;
      case '×': return firstValue * secondValue;
      case '÷': return secondValue !== 0 ? firstValue / secondValue : NaN;
      default: return secondValue;
    }
  };

  const performCalculation = useCallback(() => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const currentValue = parseFloat(previousValue);
      const newValue = calculate(currentValue, inputValue, operation);
      
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForNewValue(true);
    }
  }, [display, previousValue, operation]);

  const clear = useCallback(() => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForNewValue(false);
  }, []);

  const copyResult = useCallback(() => {
    navigator.clipboard.writeText(display);
    toast({
      title: "Copié !",
      description: "Résultat copié dans le presse-papiers",
    });
  }, [display, toast]);

  return (
    <Card className="w-full max-w-sm mx-auto shadow-lg" dir="ltr">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          {isRTL ? (
            <>
              <Button variant="outline" size="sm" onClick={copyResult}>
                <Copy className="h-4 w-4" />
              </Button>
              <CardTitle className="text-lg">{translations['home.quick_calc'] || 'Quick Calculator'}</CardTitle>
            </>
          ) : (
            <>
              <CardTitle className="text-lg">{translations['home.quick_calc'] || 'Quick Calculator'}</CardTitle>
              <Button variant="outline" size="sm" onClick={copyResult}>
                <Copy className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Display */}
        <div className="bg-gray-900 text-white p-4 rounded-lg">
          <div className="text-right text-2xl font-mono overflow-hidden">
            {display}
          </div>
          {operation && previousValue && (
            <div className="text-right text-sm text-gray-400 mt-1">
              {previousValue} {operation}
            </div>
          )}
        </div>

        {/* Buttons */}
        <div className="grid grid-cols-4 gap-2">
          <Button variant="outline" onClick={clear} className="bg-red-50 hover:bg-red-100 text-red-700">C</Button>
          <Button variant="outline" onClick={() => setDisplay(display.slice(0, -1) || '0')}>⌫</Button>
          <Button variant="outline" disabled></Button>
          <Button variant="outline" onClick={() => inputOperation('÷')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">÷</Button>
          
          <Button variant="outline" onClick={() => inputNumber('7')}>7</Button>
          <Button variant="outline" onClick={() => inputNumber('8')}>8</Button>
          <Button variant="outline" onClick={() => inputNumber('9')}>9</Button>
          <Button variant="outline" onClick={() => inputOperation('×')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">×</Button>
          
          <Button variant="outline" onClick={() => inputNumber('4')}>4</Button>
          <Button variant="outline" onClick={() => inputNumber('5')}>5</Button>
          <Button variant="outline" onClick={() => inputNumber('6')}>6</Button>
          <Button variant="outline" onClick={() => inputOperation('-')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">-</Button>
          
          <Button variant="outline" onClick={() => inputNumber('1')}>1</Button>
          <Button variant="outline" onClick={() => inputNumber('2')}>2</Button>
          <Button variant="outline" onClick={() => inputNumber('3')}>3</Button>
          <Button variant="outline" onClick={() => inputOperation('+')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">+</Button>
          
          <Button variant="outline" onClick={() => inputNumber('0')} className="col-span-2">0</Button>
          <Button variant="outline" onClick={() => inputNumber('.')}>.</Button>
          <Button onClick={performCalculation} className="bg-calculator-primary hover:bg-calculator-primary/90">=</Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default BasicCalculator;
