import {notFound} from 'next/navigation';
import {getRequestConfig} from 'next-intl/server';

// Can be imported from a shared config
export const locales = ['en', 'fr', 'es', 'ar'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

export const localeNames: Record<Locale, string> = {
  en: 'English',
  fr: 'Français',
  es: 'Español',
  ar: 'العربية'
};

export const pathnames = {
  '/': '/',
  '/scientific': {
    en: '/scientific',
    fr: '/scientifique',
    es: '/cientifica',
    ar: '/علمية'
  },
  '/financial': {
    en: '/financial',
    fr: '/financiere',
    es: '/financiera',
    ar: '/مالية'
  },
  '/conversion': {
    en: '/conversion',
    fr: '/conversion',
    es: '/conversion',
    ar: '/تحويل'
  },
  '/health': {
    en: '/health',
    fr: '/sante',
    es: '/salud',
    ar: '/صحة'
  }
} as const;

export default getRequestConfig(async ({locale}) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
