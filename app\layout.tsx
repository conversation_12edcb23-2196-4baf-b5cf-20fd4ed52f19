import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'PowerCalc - Professional Calculator Suite',
  description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',
  keywords: 'calculator, scientific calculator, financial calculator, unit converter, health calculator, BMI calculator',
  authors: [{ name: 'PowerCalc Team' }],
  creator: 'PowerCalc',
  publisher: 'PowerCalc',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://powercalc.com',
    title: 'PowerCalc - Professional Calculator Suite',
    description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',
    siteName: 'PowerCalc',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PowerCalc - Professional Calculator Suite',
    description: 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
