'use client';

import React, { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle, Calculator, Zap, Globe, Users } from 'lucide-react';

const ConversionCalculator = () => {
  const t = useTranslations('conversion');
  const tUnits = useTranslations('units');
  const locale = useLocale();
  const isRTL = locale === 'ar';
  
  const [category, setCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('');
  const [toUnit, setToUnit] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');

  const conversions: Record<string, Record<string, { name: string; factor: number }>> = {
    length: {
      meter: { name: tUnits('meter') || 'Meter', factor: 1 },
      kilometer: { name: tUnits('kilometer') || 'Kilometer', factor: 1000 },
      centimeter: { name: tUnits('centimeter') || 'Centimeter', factor: 0.01 },
      millimeter: { name: tUnits('millimeter') || 'Millimeter', factor: 0.001 },
      inch: { name: tUnits('inch') || 'Inch', factor: 0.0254 },
      foot: { name: tUnits('foot') || 'Foot', factor: 0.3048 },
      yard: { name: tUnits('yard') || 'Yard', factor: 0.9144 },
      mile: { name: tUnits('mile') || 'Mile', factor: 1609.34 },
    },
    weight: {
      kilogram: { name: tUnits('kilogram') || 'Kilogram', factor: 1 },
      gram: { name: tUnits('gram') || 'Gram', factor: 0.001 },
      pound: { name: tUnits('pound') || 'Pound', factor: 0.453592 },
      ounce: { name: tUnits('ounce') || 'Ounce', factor: 0.0283495 },
      ton: { name: tUnits('ton') || 'Ton', factor: 1000 },
    },
    temperature: {
      celsius: { name: tUnits('celsius') || 'Celsius', factor: 1 },
      fahrenheit: { name: tUnits('fahrenheit') || 'Fahrenheit', factor: 1 },
      kelvin: { name: tUnits('kelvin') || 'Kelvin', factor: 1 },
    },
    area: {
      square_meter: { name: tUnits('square_meter') || 'Square Meter', factor: 1 },
      square_kilometer: { name: tUnits('square_kilometer') || 'Square Kilometer', factor: 1000000 },
      square_centimeter: { name: tUnits('square_centimeter') || 'Square Centimeter', factor: 0.0001 },
      square_inch: { name: tUnits('square_inch') || 'Square Inch', factor: 0.00064516 },
      square_foot: { name: tUnits('square_foot') || 'Square Foot', factor: 0.092903 },
      acre: { name: tUnits('acre') || 'Acre', factor: 4046.86 },
      hectare: { name: tUnits('hectare') || 'Hectare', factor: 10000 },
    },
    volume: {
      liter: { name: tUnits('liter') || 'Liter', factor: 1 },
      milliliter: { name: tUnits('milliliter') || 'Milliliter', factor: 0.001 },
      cubic_meter: { name: tUnits('cubic_meter') || 'Cubic Meter', factor: 1000 },
      cubic_centimeter: { name: tUnits('cubic_centimeter') || 'Cubic Centimeter', factor: 0.001 },
      gallon: { name: tUnits('gallon') || 'Gallon', factor: 3.78541 },
      quart: { name: tUnits('quart') || 'Quart', factor: 0.946353 },
      pint: { name: tUnits('pint') || 'Pint', factor: 0.473176 },
      cup: { name: tUnits('cup') || 'Cup', factor: 0.236588 },
    },
    speed: {
      meter_per_second: { name: tUnits('meter_per_second') || 'Meter/Second', factor: 1 },
      kilometer_per_hour: { name: tUnits('kilometer_per_hour') || 'Kilometer/Hour', factor: 0.277778 },
      mile_per_hour: { name: tUnits('mile_per_hour') || 'Mile/Hour', factor: 0.44704 },
      foot_per_second: { name: tUnits('foot_per_second') || 'Foot/Second', factor: 0.3048 },
      knot: { name: tUnits('knot') || 'Knot', factor: 0.514444 },
    },
    energy: {
      joule: { name: tUnits('joule') || 'Joule', factor: 1 },
      kilojoule: { name: tUnits('kilojoule') || 'Kilojoule', factor: 1000 },
      calorie: { name: tUnits('calorie') || 'Calorie', factor: 4.184 },
      kilocalorie: { name: tUnits('kilocalorie') || 'Kilocalorie', factor: 4184 },
      watt_hour: { name: tUnits('watt_hour') || 'Watt Hour', factor: 3600 },
      kilowatt_hour: { name: tUnits('kilowatt_hour') || 'Kilowatt Hour', factor: 3600000 },
    },
    pressure: {
      pascal: { name: tUnits('pascal') || 'Pascal', factor: 1 },
      kilopascal: { name: tUnits('kilopascal') || 'Kilopascal', factor: 1000 },
      bar: { name: tUnits('bar') || 'Bar', factor: 100000 },
      atmosphere: { name: tUnits('atmosphere') || 'Atmosphere', factor: 101325 },
      psi: { name: tUnits('psi') || 'PSI', factor: 6894.76 },
      torr: { name: tUnits('torr') || 'Torr', factor: 133.322 },
    },
    power: {
      watt: { name: tUnits('watt') || 'Watt', factor: 1 },
      kilowatt: { name: tUnits('kilowatt') || 'Kilowatt', factor: 1000 },
      horsepower: { name: tUnits('horsepower') || 'Horsepower', factor: 745.7 },
      btu_per_hour: { name: tUnits('btu_per_hour') || 'BTU/Hour', factor: 0.293071 },
    },
  };

  const handleConvert = () => {
    if (!inputValue || !fromUnit || !toUnit) return;

    const value = parseFloat(inputValue);
    if (isNaN(value)) return;

    let convertedValue: number;

    if (category === 'temperature') {
      // Special handling for temperature conversions
      if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {
        convertedValue = (value * 9/5) + 32;
      } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {
        convertedValue = (value - 32) * 5/9;
      } else if (fromUnit === 'celsius' && toUnit === 'kelvin') {
        convertedValue = value + 273.15;
      } else if (fromUnit === 'kelvin' && toUnit === 'celsius') {
        convertedValue = value - 273.15;
      } else if (fromUnit === 'fahrenheit' && toUnit === 'kelvin') {
        convertedValue = ((value - 32) * 5/9) + 273.15;
      } else if (fromUnit === 'kelvin' && toUnit === 'fahrenheit') {
        convertedValue = ((value - 273.15) * 9/5) + 32;
      } else {
        convertedValue = value; // Same unit
      }
    } else {
      // Regular unit conversions using factors
      const fromFactor = conversions[category][fromUnit]?.factor || 1;
      const toFactor = conversions[category][toUnit]?.factor || 1;
      convertedValue = (value * fromFactor) / toFactor;
    }

    setResult(convertedValue.toFixed(6));
  };

  const categories = [
    { key: 'length', name: t('length') || 'Length' },
    { key: 'weight', name: t('weight') || 'Weight' },
    { key: 'temperature', name: t('temperature') || 'Temperature' },
    { key: 'area', name: t('area') || 'Area' },
    { key: 'volume', name: t('volume') || 'Volume' },
    { key: 'speed', name: t('speed') || 'Speed' },
    { key: 'energy', name: t('energy') || 'Energy' },
    { key: 'pressure', name: t('pressure') || 'Pressure' },
    { key: 'power', name: t('power') || 'Power' },
  ];

  // Reset units when category changes
  React.useEffect(() => {
    setFromUnit('');
    setToUnit('');
    setResult('');
  }, [category]);

  const categoryFeatures = [
    { icon: CheckCircle, key: 'seo.category1' },
    { icon: Calculator, key: 'seo.category2' },
    { icon: Zap, key: 'seo.category3' },
    { icon: Globe, key: 'seo.category4' },
    { icon: Users, key: 'seo.category5' },
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-600 mx-auto rounded-full" />
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              {t('title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Category Selection */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t('category')}
              </label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {categories.map((cat) => (
                    <SelectItem key={cat.key} value={cat.key} className={isRTL ? 'text-right' : 'text-left'}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Input Value */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t('value')}
              </label>
              <Input
                type="number"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={t('enter_value')}
                className={isRTL ? 'text-right' : 'text-left'}
              />
            </div>

            {/* From Unit */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t('from')}
              </label>
              <Select value={fromUnit} onValueChange={setFromUnit}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {Object.entries(conversions[category] || {}).map(([key, unit]) => (
                    <SelectItem key={key} value={key} className={isRTL ? 'text-right' : 'text-left'}>
                      {unit.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* To Unit */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t('to')}
              </label>
              <Select value={toUnit} onValueChange={setToUnit}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {Object.entries(conversions[category] || {}).map(([key, unit]) => (
                    <SelectItem key={key} value={key} className={isRTL ? 'text-right' : 'text-left'}>
                      {unit.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleConvert} className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700">
              {t('calculate') || 'Calculate'}
            </Button>

            {result && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t('result') || 'Result'}
                </h3>
                <p className={`text-2xl font-bold text-orange-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {result}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* SEO Content Section - Moved below the calculator */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {t('seo.intro')}
            </p>

            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {t('seo.categories_title')}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {categoryFeatures.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Icon className="h-6 w-6 text-orange-500 mt-1 flex-shrink-0" />
                    <p className="text-gray-700">{t(feature.key)}</p>
                  </div>
                );
              })}
            </div>

            <p className="text-gray-600 italic">
              {t('seo.applications')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversionCalculator;
