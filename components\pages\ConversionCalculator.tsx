'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const ConversionCalculator = () => {
  const t = useTranslations('conversion');
  const locale = useLocale();
  const isRTL = locale === 'ar';

  const categories = [
    'length', 'weight', 'temperature', 'area', 'volume', 'speed', 'energy', 'pressure', 'power'
  ];

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('seo.intro')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{t(category)}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-6 rounded-lg text-center">
                  <p className="text-gray-600 text-sm">
                    Convert {category} units
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>{t('seo.categories_title')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">{t('seo.category1')}</p>
              <p className="text-gray-600">{t('seo.category2')}</p>
              <p className="text-gray-600">{t('seo.category3')}</p>
              <p className="text-gray-600">{t('seo.category4')}</p>
              <p className="text-gray-600">{t('seo.category5')}</p>
              <div className="mt-6 pt-6 border-t">
                <p className="text-gray-600 font-medium">{t('seo.applications')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ConversionCalculator;
