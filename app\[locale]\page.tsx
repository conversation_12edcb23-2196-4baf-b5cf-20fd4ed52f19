import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import Home from '@/components/pages/Home';

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'home' });

  return {
    title: t('title') + ' - ' + t('subtitle'),
    description: t('description'),
    openGraph: {
      title: t('title') + ' - ' + t('subtitle'),
      description: t('description'),
      locale: locale,
    },
  };
}

export default function HomePage() {
  return <Home />;
}
