
export default {
  // Navigation
  'nav.home': 'Home',
  'nav.scientific': 'Scientific',
  'nav.financial': 'Financial',
  'nav.conversion': 'Conversion',
  'nav.health': 'Health',
  'nav.language': 'Language',

  // Common
  'common.calculate': 'Calculate',
  'common.clear': 'Clear',
  'common.reset': 'Reset',
  'common.copy': 'Copy',
  'common.result': 'Result',
  'common.error': 'Error',
  'common.invalid_input': 'Invalid input',

  // Home page
  'home.title': 'PowerCalc',
  'home.subtitle': 'Professional Calculator Suite',
  'home.description': 'Advanced calculators for all your computational needs. Scientific, financial, conversion, and health calculators in one powerful suite.',
  'home.get_started': 'Get Started',
  'home.quick_calc': 'Quick Calculator',
  'home.try_calculator': 'Try our quick calculator for basic calculations',
  'home.features.title': 'Powerful Features',
  'home.features.scientific': 'Scientific Calculator',
  'home.features.scientific_desc': 'Advanced mathematical functions, trigonometry, logarithms, and more.',
  'home.features.financial': 'Financial Calculator',
  'home.features.financial_desc': 'Loan calculations, interest rates, investments, and financial planning.',
  'home.features.conversion': 'Unit Converter',
  'home.features.conversion_desc': 'Convert between different units of measurement, currencies, and more.',
  'home.features.health': 'Health Calculator',
  'home.features.health_desc': 'BMI, calorie calculations, and other health-related metrics.',
  'home.cta.title': 'Ready to start calculating?',
  'home.cta.description': 'Choose the calculator that fits your needs and start computing with precision.',
  'home.seo.intro': 'PowerCalc is your comprehensive online calculator suite designed for professionals, students, and anyone who needs precise calculations. Our advanced tools combine powerful functionality with intuitive design.',
  'home.seo.benefits_title': 'Why Choose PowerCalc?',
  'home.seo.benefit1': 'Free and accessible from any device with an internet connection',
  'home.seo.benefit2': 'No downloads or installations required - use directly in your browser',
  'home.seo.benefit3': 'Multi-language support for global accessibility',
  'home.seo.benefit4': 'Professional-grade accuracy for all calculations',
  'home.seo.benefit5': 'Regular updates and new features based on user feedback',

  // Scientific Calculator
  'scientific.title': 'Scientific Calculator',
  'scientific.basic': 'Basic',
  'scientific.advanced': 'Advanced',
  'scientific.functions': 'Functions',
  'scientific.constants': 'Constants',
  'scientific.memory': 'Memory',
  'scientific.history': 'History',
  'scientific.settings': 'Settings',
  'scientific.degree': 'Deg',
  'scientific.radian': 'Rad',
  'scientific.copy_result': 'Copy Result',
  'scientific.copied': 'Copied!',
  'scientific.calculator': 'Calculator',
  'scientific.error': 'Error',
  'scientific.angle_mode': 'Angle: {mode}',
  'scientific.no_history': 'No calculations yet',
  'scientific.history_cleared': 'History cleared',
  'scientific.history_cleared_desc': 'The calculation history has been cleared.',
  'scientific.copied_desc': 'The result has been copied to the clipboard.',
  'scientific.memory_stored': 'Stored to memory',
  'scientific.memory_stored_desc': 'Value {value} has been stored to memory.',
  'scientific.memory_cleared': 'Memory cleared',
  'scientific.memory_cleared_desc': 'The memory has been cleared.',
  'scientific.memory_added': 'Added to memory',
  'scientific.memory_added_desc': '{value} has been added to memory.',
  'scientific.memory_subtracted': 'Subtracted from memory',
  'scientific.memory_subtracted_desc': '{value} has been subtracted from memory.',
  'scientific.seo.intro': 'Our advanced scientific calculator provides all the mathematical functions you need for complex calculations. Perfect for students, engineers, scientists, and researchers.',
  'scientific.seo.features_title': 'Scientific Calculator Features',
  'scientific.seo.feature1': 'Trigonometric functions (sin, cos, tan) with degree and radian modes',
  'scientific.seo.feature2': 'Logarithmic and exponential functions',
  'scientific.seo.feature3': 'Memory functions for storing and recalling values',
  'scientific.seo.feature4': 'Calculation history for reviewing previous operations',
  'scientific.seo.feature5': 'Statistical functions and probability calculations',
  'scientific.seo.use_cases': 'Perfect for physics calculations, engineering problems, mathematical research, and educational purposes.',

  // Financial Calculator
  'financial.title': 'Financial Calculator',
  'financial.loan': 'Loan Calculator',
  'financial.investment': 'Investment Calculator',
  'financial.mortgage': 'Mortgage Calculator',
  'financial.compound_interest': 'Compound Interest',
  'financial.principal': 'Principal',
  'financial.rate': 'Interest Rate (%)',
  'financial.time': 'Time (years)',
  'financial.monthly_payment': 'Monthly Payment',
  'financial.total_payment': 'Total Payment',
  'financial.total_interest': 'Total Interest',
  'financial.home_price': 'Home Price',
  'financial.down_payment': 'Down Payment',
  'financial.loan_term': 'Loan Term (years)',
  'financial.loan_amount': 'Loan Amount',
  'financial.final_amount': 'Final Amount',
  'financial.interest_earned': 'Total Interest Earned',
  'financial.compounding_frequency': 'Compounding Frequency (per year)',
  'financial.annually': 'Annually',
  'financial.semi_annually': 'Semi-annually',
  'financial.quarterly': 'Quarterly',
  'financial.monthly': 'Monthly',
  'financial.daily': 'Daily',
  'financial.seo.intro': 'Make informed financial decisions with our comprehensive financial calculator suite. Calculate loans, mortgages, investments, and compound interest with professional accuracy.',
  'financial.seo.tools_title': 'Financial Planning Tools',
  'financial.seo.tool1': 'Loan calculator for personal loans, auto loans, and business financing',
  'financial.seo.tool2': 'Mortgage calculator with amortization schedules',
  'financial.seo.tool3': 'Investment calculator for retirement and savings planning',
  'financial.seo.tool4': 'Compound interest calculator for long-term growth projections',
  'financial.seo.benefits': 'Essential for financial advisors, loan officers, real estate professionals, and anyone planning their financial future.',

  // Conversion Calculator
  'conversion.title': 'Unit Converter',
  'conversion.length': 'Length',
  'conversion.weight': 'Weight',
  'conversion.temperature': 'Temperature',
  'conversion.area': 'Area',
  'conversion.volume': 'Volume',
  'conversion.speed': 'Speed',
  'conversion.energy': 'Energy',
  'conversion.pressure': 'Pressure',
  'conversion.power': 'Power',
  'conversion.from': 'From',
  'conversion.to': 'To',
  'conversion.value': 'Value',
  'conversion.category': 'Category',
  'conversion.enter_value': 'Enter value',
  'conversion.seo.intro': 'Convert between hundreds of units across multiple categories with our precise unit conversion calculator. Supporting metric, imperial, and specialized measurement systems.',
  'conversion.seo.categories_title': 'Conversion Categories',
  'conversion.seo.category1': 'Length: meters, feet, inches, kilometers, miles, and more',
  'conversion.seo.category2': 'Weight: kilograms, pounds, ounces, tons, and specialty units',
  'conversion.seo.category3': 'Temperature: Celsius, Fahrenheit, and Kelvin conversions',
  'conversion.seo.category4': 'Area: square meters, acres, hectares, and building measurements',
  'conversion.seo.category5': 'Volume: liters, gallons, cubic measurements, and cooking units',
  'conversion.seo.applications': 'Ideal for engineers, architects, cooks, travelers, and students working with international measurements.',

  // Health Calculator
  'health.title': 'Health Calculator',
  'health.bmi': 'BMI Calculator',
  'health.bmr': 'BMR Calculator',
  'health.calories': 'Calorie Calculator',
  'health.weight': 'Weight (kg)',
  'health.height': 'Height (cm)',
  'health.age': 'Age',
  'health.gender': 'Gender',
  'health.male': 'Male',
  'health.female': 'Female',
  'health.activity_level': 'Activity Level',
  'health.sedentary': 'Sedentary',
  'health.light': 'Light Activity',
  'health.moderate': 'Moderate Activity',
  'health.active': 'Very Active',
  'health.extremely_active': 'Extremely Active',
  'health.bmi_result': 'Your BMI',
  'health.bmr_result': 'Your BMR',
  'health.calories_result': 'Daily Calories',
  'health.underweight': 'Underweight',
  'health.normal': 'Normal Weight',
  'health.overweight': 'Overweight',
  'health.obese': 'Obese',
  'health.bmi_scale': 'BMI Scale',
  'health.maintenance': 'Maintenance',
  'health.weight_loss': 'Weight Loss',
  'health.weight_gain': 'Weight Gain',
  'health.calories_per_day': 'calories/day',
  'health.calories_at_rest': 'calories/day at rest',
  'health.calories_maintain': 'calories/day to maintain weight',
  'health.calories_lose': 'calories/day to lose 0.5kg/week',
  'health.calories_gain': 'calories/day to gain 0.5kg/week',
  'health.bmr_description': 'Your Basal Metabolic Rate (BMR) is the number of calories your body needs to function at rest.',
  'health.seo.intro': 'Monitor and improve your health with our comprehensive health calculator suite. Calculate BMI, BMR, daily calorie needs, and track your wellness journey.',
  'health.seo.calculators_title': 'Health Assessment Tools',
  'health.seo.calculator1': 'BMI Calculator: Assess your body mass index and health category',
  'health.seo.calculator2': 'BMR Calculator: Determine your basal metabolic rate',
  'health.seo.calculator3': 'Calorie Calculator: Plan your daily nutrition based on activity level',
  'health.seo.calculator4': 'Weight management tools for healthy lifestyle planning',
  'health.seo.disclaimer': 'These calculations are for informational purposes only and should not replace professional medical advice.',

  // Basic Calculator
  'basic.title': 'Basic Calculator',
  'basic.placeholder': 'Enter calculation...',

  // Units - Length
  'units.meter': 'Meter',
  'units.kilometer': 'Kilometer',
  'units.centimeter': 'Centimeter',
  'units.millimeter': 'Millimeter',
  'units.inch': 'Inch',
  'units.foot': 'Foot',
  'units.yard': 'Yard',
  'units.mile': 'Mile',

  // Units - Weight
  'units.kilogram': 'Kilogram',
  'units.gram': 'Gram',
  'units.pound': 'Pound',
  'units.ounce': 'Ounce',
  'units.ton': 'Ton',

  // Units - Temperature
  'units.celsius': 'Celsius',
  'units.fahrenheit': 'Fahrenheit',
  'units.kelvin': 'Kelvin',

  // Units - Area
  'units.square_meter': 'Square Meter',
  'units.square_kilometer': 'Square Kilometer',
  'units.square_centimeter': 'Square Centimeter',
  'units.square_inch': 'Square Inch',
  'units.square_foot': 'Square Foot',
  'units.acre': 'Acre',
  'units.hectare': 'Hectare',

  // Units - Volume
  'units.liter': 'Liter',
  'units.milliliter': 'Milliliter',
  'units.cubic_meter': 'Cubic Meter',
  'units.cubic_centimeter': 'Cubic Centimeter',
  'units.gallon': 'Gallon',
  'units.quart': 'Quart',
  'units.pint': 'Pint',
  'units.cup': 'Cup',

  // Units - Speed
  'units.meter_per_second': 'Meter per Second',
  'units.kilometer_per_hour': 'Kilometer per Hour',
  'units.mile_per_hour': 'Mile per Hour',
  'units.foot_per_second': 'Foot per Second',
  'units.knot': 'Knot',

  // Units - Energy
  'units.joule': 'Joule',
  'units.kilojoule': 'Kilojoule',
  'units.calorie': 'Calorie',
  'units.kilocalorie': 'Kilocalorie',
  'units.watt_hour': 'Watt Hour',
  'units.kilowatt_hour': 'Kilowatt Hour',

  // Units - Pressure
  'units.pascal': 'Pascal',
  'units.kilopascal': 'Kilopascal',
  'units.bar': 'Bar',
  'units.atmosphere': 'Atmosphere',
  'units.psi': 'PSI',
  'units.torr': 'Torr',

  // Units - Power
  'units.watt': 'Watt',
  'units.kilowatt': 'Kilowatt',
  'units.horsepower': 'Horsepower',
  'units.btu_per_hour': 'BTU per Hour',
};
