import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import ConversionCalculator from '@/components/pages/ConversionCalculator';

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'conversion' });

  return {
    title: t('title'),
    description: t('seo.intro'),
    openGraph: {
      title: t('title'),
      description: t('seo.intro'),
      locale: locale,
    },
  };
}

export default function ConversionCalculatorPage() {
  return <ConversionCalculator />;
}
