"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/scientific/page",{

/***/ "(app-pages-browser)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ScientificCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    // Keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScientificCalculator.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ScientificCalculator.useEffect.handleKeyPress\": (event)=>{\n                    const { key } = event;\n                    if (key >= '0' && key <= '9') {\n                        inputNumber(key);\n                    } else if (key === '.') {\n                        inputNumber('.');\n                    } else if (key === '+') {\n                        inputOperation('+');\n                    } else if (key === '-') {\n                        inputOperation('-');\n                    } else if (key === '*') {\n                        inputOperation('×');\n                    } else if (key === '/') {\n                        event.preventDefault();\n                        inputOperation('÷');\n                    } else if (key === 'Enter' || key === '=') {\n                        performCalculation();\n                    } else if (key === 'Escape' || key === 'c' || key === 'C') {\n                        clear();\n                    } else if (key === 'Backspace') {\n                        setDisplay(display.slice(0, -1) || '0');\n                    }\n                }\n            }[\"ScientificCalculator.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ScientificCalculator.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ScientificCalculator.useEffect\"];\n        }\n    }[\"ScientificCalculator.useEffect\"], [\n        display\n    ]);\n    // Helper functions\n    const toRadians = (degrees)=>degrees * (Math.PI / 180);\n    const toDegrees = (radians)=>radians * (180 / Math.PI);\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[addToHistory]\": (calculation)=>{\n            setHistory({\n                \"ScientificCalculator.useCallback[addToHistory]\": (prev)=>[\n                        calculation,\n                        ...prev.slice(0, 9)\n                    ]\n            }[\"ScientificCalculator.useCallback[addToHistory]\"]);\n        }\n    }[\"ScientificCalculator.useCallback[addToHistory]\"], []);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' && num !== '.' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performCalculation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performCalculation]\": ()=>{\n            const inputValue = parseFloat(display);\n            if (previousValue !== null && operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(null);\n                setOperation(null);\n                setWaitingForNewValue(true);\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n        }\n    }[\"ScientificCalculator.useCallback[performCalculation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const performFunction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performFunction]\": (func)=>{\n            const value = parseFloat(display);\n            let result;\n            switch(func){\n                case 'sin':\n                    result = Math.sin(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'cos':\n                    result = Math.cos(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'tan':\n                    result = Math.tan(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'ln':\n                    result = Math.log(value);\n                    break;\n                case 'log':\n                    result = Math.log10(value);\n                    break;\n                case 'sqrt':\n                    result = Math.sqrt(value);\n                    break;\n                case 'square':\n                    result = value * value;\n                    break;\n                case 'factorial':\n                    result = value <= 0 ? 1 : Array.from({\n                        length: value\n                    }, {\n                        \"ScientificCalculator.useCallback[performFunction]\": (_, i)=>i + 1\n                    }[\"ScientificCalculator.useCallback[performFunction]\"]).reduce({\n                        \"ScientificCalculator.useCallback[performFunction]\": (a, b)=>a * b\n                    }[\"ScientificCalculator.useCallback[performFunction]\"], 1);\n                    break;\n                case 'reciprocal':\n                    result = 1 / value;\n                    break;\n                default:\n                    result = value;\n            }\n            setDisplay(String(result));\n            setWaitingForNewValue(true);\n            addToHistory(\"\".concat(func, \"(\").concat(value, \") = \").concat(result));\n        }\n    }[\"ScientificCalculator.useCallback[performFunction]\"], [\n        display,\n        angleMode,\n        addToHistory\n    ]);\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'pi':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    t('calculator')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: angleMode === 'deg' ? 'default' : 'secondary',\n                                                        children: t('angle_mode', {\n                                                            mode: angleMode === 'deg' ? t('degree') : t('radian')\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setAngleMode(angleMode === 'deg' ? 'rad' : 'deg'),\n                                                        children: angleMode === 'deg' ? 'RAD' : 'DEG'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: copyResult,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            defaultValue: \"basic\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                                    className: \"grid w-full grid-cols-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"basic\",\n                                                            children: t('basic')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"functions\",\n                                                            children: t('functions')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"constants\",\n                                                            children: t('constants')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-mono \".concat(isRTL ? 'text-left' : 'text-right', \" mb-2\"),\n                                                                children: display\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            operation && previousValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 \".concat(isRTL ? 'text-left' : 'text-right'),\n                                                                children: [\n                                                                    previousValue,\n                                                                    \" \",\n                                                                    operation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t('angle_mode', {\n                                                                            mode: angleMode === 'deg' ? 'DEG' : 'RAD'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    memory !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"M: \",\n                                                                            memory\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"basic\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: clear,\n                                                                className: \"bg-red-50 hover:bg-red-100 text-red-700\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.slice(0, -1) || '0'),\n                                                                children: \"⌫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('^'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x^y\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sqrt'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"√\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('÷'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xf7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('7'),\n                                                                children: \"7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('8'),\n                                                                children: \"8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('9'),\n                                                                children: \"9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('square'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x\\xb2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('×'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('4'),\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('5'),\n                                                                children: \"5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('6'),\n                                                                children: \"6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('reciprocal'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"1/x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('-'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('1'),\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('2'),\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('3'),\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('factorial'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"n!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('+'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('0'),\n                                                                className: \"col-span-2\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('.'),\n                                                                children: \".\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.startsWith('-') ? display.slice(1) : '-' + display),\n                                                                children: \"\\xb1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: performCalculation,\n                                                                className: \"bg-calculator-primary hover:bg-calculator-primary/90 text-white\",\n                                                                children: \"=\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"advanced\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sin'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"sin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('cos'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"cos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('tan'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"tan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('ln'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"ln\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('log'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"log\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"functions\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-4 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryStore,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryRecall,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryAdd,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"M+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryClear,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"constants\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('pi'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('e'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"e\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        t('history')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: clearHistory,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                                                children: history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 text-center py-4\",\n                                                    children: t('no_history')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, undefined) : history.map((calc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs bg-gray-50 p-2 rounded font-mono\",\n                                                        children: calc\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: t('seo.features_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Advanced Mathematical Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Logarithmic & Exponential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature2')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Memory Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature3')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Calculation History\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature4')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Perfect For:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: t('seo.use_cases')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScientificCalculator, \"damFe7X5V9GwC4ZaS5rVaTG1/Uo=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = ScientificCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\nvar _c;\n$RefreshReg$(_c, \"ScientificCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ScientificCalculator.tsx\n"));

/***/ })

});