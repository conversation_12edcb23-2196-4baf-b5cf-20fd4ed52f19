"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/scientific/page",{

/***/ "(app-pages-browser)/./components/pages/ScientificCalculator.tsx":
/*!***************************************************!*\
  !*** ./components/pages/ScientificCalculator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Copy,History,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ScientificCalculator = ()=>{\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('scientific');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const isRTL = locale === 'ar';\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const [previousValue, setPreviousValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [operation, setOperation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [waitingForNewValue, setWaitingForNewValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [angleMode, setAngleMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('deg');\n    // Keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScientificCalculator.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ScientificCalculator.useEffect.handleKeyPress\": (event)=>{\n                    const { key } = event;\n                    if (key >= '0' && key <= '9') {\n                        inputNumber(key);\n                    } else if (key === '.') {\n                        inputNumber('.');\n                    } else if (key === '+') {\n                        inputOperation('+');\n                    } else if (key === '-') {\n                        inputOperation('-');\n                    } else if (key === '*') {\n                        inputOperation('×');\n                    } else if (key === '/') {\n                        event.preventDefault();\n                        inputOperation('÷');\n                    } else if (key === 'Enter' || key === '=') {\n                        performCalculation();\n                    } else if (key === 'Escape' || key === 'c' || key === 'C') {\n                        clear();\n                    } else if (key === 'Backspace') {\n                        setDisplay(display.slice(0, -1) || '0');\n                    }\n                }\n            }[\"ScientificCalculator.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ScientificCalculator.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ScientificCalculator.useEffect\"];\n        }\n    }[\"ScientificCalculator.useEffect\"], [\n        display\n    ]);\n    const inputNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputNumber]\": (num)=>{\n            if (waitingForNewValue) {\n                setDisplay(num);\n                setWaitingForNewValue(false);\n            } else {\n                if (num === '.' && display.includes('.')) return;\n                setDisplay(display === '0' ? num : display + num);\n            }\n        }\n    }[\"ScientificCalculator.useCallback[inputNumber]\"], [\n        display,\n        waitingForNewValue\n    ]);\n    const inputOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[inputOperation]\": (nextOperation)=>{\n            const inputValue = parseFloat(display);\n            if (previousValue === null) {\n                setPreviousValue(display);\n            } else if (operation) {\n                const currentValue = previousValue || '0';\n                const newValue = calculate(parseFloat(currentValue), inputValue, operation);\n                const historyEntry = \"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue);\n                setHistory({\n                    \"ScientificCalculator.useCallback[inputOperation]\": (prev)=>[\n                            historyEntry,\n                            ...prev.slice(0, 19)\n                        ]\n                }[\"ScientificCalculator.useCallback[inputOperation]\"]);\n                setDisplay(String(newValue));\n                setPreviousValue(String(newValue));\n            }\n            setWaitingForNewValue(true);\n            setOperation(nextOperation);\n        }\n    }[\"ScientificCalculator.useCallback[inputOperation]\"], [\n        display,\n        previousValue,\n        operation\n    ]);\n    const calculate = (firstValue, secondValue, operation)=>{\n        switch(operation){\n            case '+':\n                return firstValue + secondValue;\n            case '-':\n                return firstValue - secondValue;\n            case '×':\n                return firstValue * secondValue;\n            case '÷':\n                return secondValue !== 0 ? firstValue / secondValue : NaN;\n            case '^':\n                return Math.pow(firstValue, secondValue);\n            case 'mod':\n                return firstValue % secondValue;\n            case 'xroot':\n                return Math.pow(secondValue, 1 / firstValue);\n            default:\n                return secondValue;\n        }\n    };\n    const performCalculation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performCalculation]\": ()=>{\n            const inputValue = parseFloat(display);\n            if (previousValue !== null && operation) {\n                const currentValue = parseFloat(previousValue);\n                const newValue = calculate(currentValue, inputValue, operation);\n                setDisplay(String(newValue));\n                setPreviousValue(null);\n                setOperation(null);\n                setWaitingForNewValue(true);\n                addToHistory(\"\".concat(currentValue, \" \").concat(operation, \" \").concat(inputValue, \" = \").concat(newValue));\n            }\n        }\n    }[\"ScientificCalculator.useCallback[performCalculation]\"], [\n        display,\n        previousValue,\n        operation,\n        addToHistory\n    ]);\n    const performFunction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[performFunction]\": (func)=>{\n            const value = parseFloat(display);\n            let result;\n            switch(func){\n                case 'sin':\n                    result = Math.sin(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'cos':\n                    result = Math.cos(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'tan':\n                    result = Math.tan(angleMode === 'deg' ? toRadians(value) : value);\n                    break;\n                case 'ln':\n                    result = Math.log(value);\n                    break;\n                case 'log':\n                    result = Math.log10(value);\n                    break;\n                case 'sqrt':\n                    result = Math.sqrt(value);\n                    break;\n                case 'square':\n                    result = value * value;\n                    break;\n                case 'factorial':\n                    result = value <= 0 ? 1 : Array.from({\n                        length: value\n                    }, {\n                        \"ScientificCalculator.useCallback[performFunction]\": (_, i)=>i + 1\n                    }[\"ScientificCalculator.useCallback[performFunction]\"]).reduce({\n                        \"ScientificCalculator.useCallback[performFunction]\": (a, b)=>a * b\n                    }[\"ScientificCalculator.useCallback[performFunction]\"], 1);\n                    break;\n                case 'reciprocal':\n                    result = 1 / value;\n                    break;\n                default:\n                    result = value;\n            }\n            setDisplay(String(result));\n            setWaitingForNewValue(true);\n            addToHistory(\"\".concat(func, \"(\").concat(value, \") = \").concat(result));\n        }\n    }[\"ScientificCalculator.useCallback[performFunction]\"], [\n        display,\n        angleMode,\n        addToHistory\n    ]);\n    const insertConstant = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[insertConstant]\": (constant)=>{\n            let value;\n            switch(constant){\n                case 'pi':\n                    value = Math.PI;\n                    break;\n                case 'e':\n                    value = Math.E;\n                    break;\n                default:\n                    return;\n            }\n            setDisplay(String(value));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[insertConstant]\"], []);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clear]\": ()=>{\n            setDisplay('0');\n            setPreviousValue(null);\n            setOperation(null);\n            setWaitingForNewValue(false);\n        }\n    }[\"ScientificCalculator.useCallback[clear]\"], []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[clearHistory]\": ()=>{\n            setHistory([]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('history_cleared'),\n                description: t('history_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[clearHistory]\"], [\n        t\n    ]);\n    const copyResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[copyResult]\": ()=>{\n            navigator.clipboard.writeText(display);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('copied'),\n                description: t('copied_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[copyResult]\"], [\n        display,\n        t\n    ]);\n    // Memory functions\n    const memoryStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryStore]\": ()=>{\n            setMemory(parseFloat(display));\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_stored'),\n                description: t('memory_stored_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryStore]\"], [\n        display,\n        t\n    ]);\n    const memoryRecall = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryRecall]\": ()=>{\n            setDisplay(String(memory));\n            setWaitingForNewValue(true);\n        }\n    }[\"ScientificCalculator.useCallback[memoryRecall]\"], [\n        memory\n    ]);\n    const memoryClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryClear]\": ()=>{\n            setMemory(0);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_cleared'),\n                description: t('memory_cleared_desc')\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryClear]\"], [\n        t\n    ]);\n    const memoryAdd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memoryAdd]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memoryAdd]\": (prev)=>prev + parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memoryAdd]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_added'),\n                description: t('memory_added_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memoryAdd]\"], [\n        display,\n        t\n    ]);\n    const memorySubtract = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ScientificCalculator.useCallback[memorySubtract]\": ()=>{\n            setMemory({\n                \"ScientificCalculator.useCallback[memorySubtract]\": (prev)=>prev - parseFloat(display)\n            }[\"ScientificCalculator.useCallback[memorySubtract]\"]);\n            (0,sonner__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: t('memory_subtracted'),\n                description: t('memory_subtracted_desc', {\n                    value: display\n                })\n            });\n        }\n    }[\"ScientificCalculator.useCallback[memorySubtract]\"], [\n        display,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8 \".concat(isRTL ? 'font-arabic' : ''),\n        dir: isRTL ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: t('seo.intro')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    t('calculator')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: angleMode === 'deg' ? 'default' : 'secondary',\n                                                        children: t('angle_mode', {\n                                                            mode: angleMode === 'deg' ? t('degree') : t('radian')\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setAngleMode(angleMode === 'deg' ? 'rad' : 'deg'),\n                                                        children: angleMode === 'deg' ? 'RAD' : 'DEG'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: copyResult,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            defaultValue: \"basic\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                                    className: \"grid w-full grid-cols-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"basic\",\n                                                            children: t('basic')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"functions\",\n                                                            children: t('functions')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                            value: \"constants\",\n                                                            children: t('constants')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"my-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-white p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-mono \".concat(isRTL ? 'text-left' : 'text-right', \" mb-2\"),\n                                                                children: display\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            operation && previousValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 \".concat(isRTL ? 'text-left' : 'text-right'),\n                                                                children: [\n                                                                    previousValue,\n                                                                    \" \",\n                                                                    operation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t('angle_mode', {\n                                                                            mode: angleMode === 'deg' ? 'DEG' : 'RAD'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    memory !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"M: \",\n                                                                            memory\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 42\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"basic\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: clear,\n                                                                className: \"bg-red-50 hover:bg-red-100 text-red-700\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.slice(0, -1) || '0'),\n                                                                children: \"⌫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('^'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x^y\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sqrt'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"√\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('÷'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xf7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('7'),\n                                                                children: \"7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('8'),\n                                                                children: \"8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('9'),\n                                                                children: \"9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('square'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"x\\xb2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('×'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('4'),\n                                                                children: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('5'),\n                                                                children: \"5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('6'),\n                                                                children: \"6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('reciprocal'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"1/x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('-'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('1'),\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('2'),\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('3'),\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('factorial'),\n                                                                className: \"bg-blue-50 hover:bg-blue-100 text-blue-700\",\n                                                                children: \"n!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputOperation('+'),\n                                                                className: \"bg-orange-100 hover:bg-orange-200 text-orange-800\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('0'),\n                                                                className: \"col-span-2\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>inputNumber('.'),\n                                                                children: \".\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setDisplay(display.startsWith('-') ? display.slice(1) : '-' + display),\n                                                                children: \"\\xb1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: performCalculation,\n                                                                className: \"bg-calculator-primary hover:bg-calculator-primary/90 text-white\",\n                                                                children: \"=\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"advanced\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-5 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('sin'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"sin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('cos'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"cos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('tan'),\n                                                                className: \"bg-purple-50 hover:bg-purple-100 text-purple-700\",\n                                                                children: \"tan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('ln'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"ln\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>performFunction('log'),\n                                                                className: \"bg-green-50 hover:bg-green-100 text-green-700\",\n                                                                children: \"log\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"functions\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-4 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryStore,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryRecall,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryAdd,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"M+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: memoryClear,\n                                                                className: \"bg-yellow-50 hover:bg-yellow-100 text-yellow-700\",\n                                                                children: \"MC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                                    value: \"constants\",\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('pi'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"π\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>insertConstant('e'),\n                                                                className: \"bg-indigo-50 hover:bg-indigo-100 text-indigo-700\",\n                                                                children: \"e\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"flex flex-row items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        t('history')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: clearHistory,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Copy_History_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                                                children: history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 text-center py-4\",\n                                                    children: t('no_history')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, undefined) : history.map((calc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs bg-gray-50 p-2 rounded font-mono\",\n                                                        children: calc\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: t('seo.features_title')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature1')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature2')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature3')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature4')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: t('seo.feature5')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Use Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t('seo.use_cases')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-white rounded-2xl shadow-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isRTL ? 'text-right' : 'text-left'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: t('seo.features_title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Advanced Mathematical Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Logarithmic & Exponential\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature2')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Memory Functions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature3')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Calculation History\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: t('seo.feature4')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"Perfect For:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: t('seo.use_cases')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Apps\\\\calculator\\\\components\\\\pages\\\\ScientificCalculator.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScientificCalculator, \"Gc1ClGvgIA+5uVv6tYgrd/svHao=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = ScientificCalculator;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScientificCalculator);\nvar _c;\n$RefreshReg$(_c, \"ScientificCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/ScientificCalculator.tsx\n"));

/***/ })

});