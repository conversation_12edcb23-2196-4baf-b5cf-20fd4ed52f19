
import React, { useState } from 'react';
import { Heart, Calculator, Scale, Activity, Target, Users, BookOpen, Shield, Zap } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import { Alert, AlertDescription } from '../components/ui/alert';

const HealthCalculator = () => {
  const { translations, isRTL } = useLanguage();

  // BMI State
  const [bmiWeight, setBmiWeight] = useState('');
  const [bmiHeight, setBmiHeight] = useState('');
  const [bmiResult, setBmiResult] = useState<{
    bmi: number;
    category: string;
    color: string;
  } | null>(null);

  // BMR State
  const [bmrWeight, setBmrWeight] = useState('');
  const [bmrHeight, setBmrHeight] = useState('');
  const [bmrAge, setBmrAge] = useState('');
  const [bmrGender, setBmrGender] = useState('');
  const [bmrResult, setBmrResult] = useState<number | null>(null);

  // Calorie State
  const [calorieWeight, setCalorieWeight] = useState('');
  const [calorieHeight, setCalorieHeight] = useState('');
  const [calorieAge, setCalorieAge] = useState('');
  const [calorieGender, setCalorieGender] = useState('');
  const [activityLevel, setActivityLevel] = useState('');
  const [calorieResult, setCalorieResult] = useState<{
    bmr: number;
    maintenance: number;
    loseWeight: number;
    gainWeight: number;
  } | null>(null);

  const [activeTab, setActiveTab] = useState('bmi');

  const calculateBMI = () => {
    const weight = parseFloat(bmiWeight);
    const height = parseFloat(bmiHeight) / 100; // Convert cm to m

    if (weight && height) {
      const bmi = weight / (height * height);
      let category = '';
      let color = '';

      if (bmi < 18.5) {
        category = translations['health.underweight'];
        color = 'text-blue-600';
      } else if (bmi < 25) {
        category = translations['health.normal'];
        color = 'text-green-600';
      } else if (bmi < 30) {
        category = translations['health.overweight'];
        color = 'text-yellow-600';
      } else {
        category = translations['health.obese'];
        color = 'text-red-600';
      }

      setBmiResult({ bmi, category, color });
    }
  };

  const calculateBMR = () => {
    const weight = parseFloat(bmrWeight);
    const height = parseFloat(bmrHeight);
    const age = parseFloat(bmrAge);

    if (weight && height && age && bmrGender) {
      let bmr: number;
      
      if (bmrGender === 'male') {
        // Mifflin-St Jeor Equation for men
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        // Mifflin-St Jeor Equation for women
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }

      setBmrResult(bmr);
    }
  };

  const calculateCalories = () => {
    const weight = parseFloat(calorieWeight);
    const height = parseFloat(calorieHeight);
    const age = parseFloat(calorieAge);

    if (weight && height && age && calorieGender && activityLevel) {
      let bmr: number;
      
      if (calorieGender === 'male') {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }

      const activityMultipliers = {
        'sedentary': 1.2,
        'light': 1.375,
        'moderate': 1.55,
        'active': 1.725,
        'extremely_active': 1.9
      };

      const multiplier = activityMultipliers[activityLevel as keyof typeof activityMultipliers];
      const maintenance = bmr * multiplier;
      const loseWeight = maintenance - 500; // 500 calorie deficit for ~1 lb/week weight loss
      const gainWeight = maintenance + 500; // 500 calorie surplus for ~1 lb/week weight gain

      setCalorieResult({
        bmr,
        maintenance,
        loseWeight,
        gainWeight
      });
    }
  };

  const getBMIProgressWidth = (bmi: number) => {
    if (bmi < 18.5) return Math.min((bmi / 18.5) * 25, 25);
    if (bmi < 25) return 25 + ((bmi - 18.5) / (25 - 18.5)) * 25;
    if (bmi < 30) return 50 + ((bmi - 25) / (30 - 25)) * 25;
    return Math.min(75 + ((bmi - 30) / 10) * 25, 100);
  };

  const tabsConfig = [
    { value: 'bmi', icon: Heart, label: translations['health.bmi'] },
    { value: 'bmr', icon: Activity, label: translations['health.bmr'] },
    { value: 'calories', icon: Calculator, label: translations['health.calories'] },
  ];

  const displayedTabs = isRTL ? [...tabsConfig].reverse() : tabsConfig;

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Main Calculator Interface */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {translations['health.title']}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-rose-600 mx-auto rounded-full" />
        </div>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm mb-8">
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3">
              <Heart className="h-8 w-8 text-pink-600" />
              {translations['health.title']}
            </CardTitle>
            <CardDescription className="text-lg text-gray-600">
              Calculate BMI, BMR, and daily calorie requirements for optimal health
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className={`grid w-full grid-cols-3 mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {displayedTabs.map((tab) => (
                  <TabsTrigger key={tab.value} value={tab.value} className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <tab.icon className="h-4 w-4" />
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* BMI Calculator */}
              <TabsContent value="bmi">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Heart className="h-5 w-5 text-pink-600" />
                        {translations['health.bmi']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="bmiWeight" className={isRTL ? 'text-right block' : ''}>{translations['health.weight']}</Label>
                          <Input
                            id="bmiWeight"
                            type="number"
                            value={bmiWeight}
                            onChange={(e) => setBmiWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmiHeight" className={isRTL ? 'text-right block' : ''}>{translations['health.height']}</Label>
                          <Input
                            id="bmiHeight"
                            type="number"
                            value={bmiHeight}
                            onChange={(e) => setBmiHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                      </div>
                      <Button onClick={calculateBMI} className="w-full bg-pink-600 hover:bg-pink-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {bmiResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['health.bmi_result']}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="text-center">
                          <div className="text-4xl font-bold text-pink-600 mb-2">
                            {bmiResult.bmi.toFixed(1)}
                          </div>
                          <div className={`text-lg font-semibold ${bmiResult.color}`}>
                            {bmiResult.category}
                          </div>
                        </div>
                        
                        {/* BMI Scale */}
                        <div className="space-y-2">
                          <div className={`text-sm text-gray-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.bmi_scale']}</div>
                          <div className="relative h-4 bg-gradient-to-r from-blue-400 via-green-400 via-yellow-400 to-red-400 rounded-full">
                            <div 
                              className="absolute top-0 w-2 h-4 bg-gray-800 rounded-full transform -translate-x-1"
                              style={{ left: `${getBMIProgressWidth(bmiResult.bmi)}%` }}
                            />
                          </div>
                          <div className={`flex justify-between text-xs text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>{translations['health.underweight']}</span>
                            <span>{translations['health.normal']}</span>
                            <span>{translations['health.overweight']}</span>
                            <span>{translations['health.obese']}</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="p-2 bg-blue-50 rounded">
                            <div className={`text-blue-600 font-medium ${isRTL ? 'text-right' : ''}`}>{translations['health.underweight']}</div>
                            <div className={`text-blue-800 ${isRTL ? 'text-right' : ''}`}>&lt; 18.5</div>
                          </div>
                          <div className="p-2 bg-green-50 rounded">
                            <div className={`text-green-600 font-medium ${isRTL ? 'text-right' : ''}`}>{translations['health.normal']}</div>
                            <div className={`text-green-800 ${isRTL ? 'text-right' : ''}`}>18.5 - 24.9</div>
                          </div>
                          <div className="p-2 bg-yellow-50 rounded">
                            <div className={`text-yellow-600 font-medium ${isRTL ? 'text-right' : ''}`}>{translations['health.overweight']}</div>
                            <div className={`text-yellow-800 ${isRTL ? 'text-right' : ''}`}>25 - 29.9</div>
                          </div>
                          <div className="p-2 bg-red-50 rounded">
                            <div className={`text-red-600 font-medium ${isRTL ? 'text-right' : ''}`}>{translations['health.obese']}</div>
                            <div className={`text-red-800 ${isRTL ? 'text-right' : ''}`}>≥ 30</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* BMR Calculator */}
              <TabsContent value="bmr">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Activity className="h-5 w-5 text-blue-600" />
                        {translations['health.bmr']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="bmrWeight" className={isRTL ? 'text-right block' : ''}>{translations['health.weight']}</Label>
                          <Input
                            id="bmrWeight"
                            type="number"
                            value={bmrWeight}
                            onChange={(e) => setBmrWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmrHeight" className={isRTL ? 'text-right block' : ''}>{translations['health.height']}</Label>
                          <Input
                            id="bmrHeight"
                            type="number"
                            value={bmrHeight}
                            onChange={(e) => setBmrHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bmrAge" className={isRTL ? 'text-right block' : ''}>{translations['health.age']}</Label>
                          <Input
                            id="bmrAge"
                            type="number"
                            value={bmrAge}
                            onChange={(e) => setBmrAge(e.target.value)}
                            placeholder="30"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{translations['health.gender']}</Label>
                          <Select value={bmrGender} onValueChange={setBmrGender}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={translations['health.gender']} />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                              <SelectItem value="male">{translations['health.male']}</SelectItem>
                              <SelectItem value="female">{translations['health.female']}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <Button onClick={calculateBMR} className="w-full bg-blue-600 hover:bg-blue-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {bmrResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['health.bmr_result']}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center">
                          <div className="text-4xl font-bold text-blue-600 mb-2">
                            {Math.round(bmrResult)}
                          </div>
                          <div className="text-lg text-gray-600">{translations['health.calories_per_day']}</div>
                          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                            <p className={`text-sm text-gray-700 ${isRTL ? 'text-right' : ''}`}>
                              {translations['health.bmr_description']}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Calorie Calculator */}
              <TabsContent value="calories">
                <div className={`flex flex-col lg:flex-row gap-8 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
                  <Card className="shadow-lg w-full lg:w-1/2">
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse text-right' : ''}`}>
                        <Calculator className="h-5 w-5 text-green-600" />
                        {translations['health.calories']}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="calorieWeight" className={isRTL ? 'text-right block' : ''}>{translations['health.weight']}</Label>
                          <Input
                            id="calorieWeight"
                            type="number"
                            value={calorieWeight}
                            onChange={(e) => setCalorieWeight(e.target.value)}
                            placeholder="70"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="calorieHeight" className={isRTL ? 'text-right block' : ''}>{translations['health.height']}</Label>
                          <Input
                            id="calorieHeight"
                            type="number"
                            value={calorieHeight}
                            onChange={(e) => setCalorieHeight(e.target.value)}
                            placeholder="175"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label htmlFor="calorieAge" className={isRTL ? 'text-right block' : ''}>{translations['health.age']}</Label>
                          <Input
                            id="calorieAge"
                            type="number"
                            value={calorieAge}
                            onChange={(e) => setCalorieAge(e.target.value)}
                            placeholder="30"
                            className={isRTL ? 'text-right' : ''}
                          />
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{translations['health.gender']}</Label>
                          <Select value={calorieGender} onValueChange={setCalorieGender}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={translations['health.gender']} />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                              <SelectItem value="male">{translations['health.male']}</SelectItem>
                              <SelectItem value="female">{translations['health.female']}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className={isRTL ? 'text-right block' : ''}>{translations['health.activity_level']}</Label>
                          <Select value={activityLevel} onValueChange={setActivityLevel}>
                            <SelectTrigger className={isRTL ? 'text-right' : ''}>
                              <SelectValue placeholder={translations['health.activity_level']} />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
                              <SelectItem value="sedentary">{translations['health.sedentary']}</SelectItem>
                              <SelectItem value="light">{translations['health.light']}</SelectItem>
                              <SelectItem value="moderate">{translations['health.moderate']}</SelectItem>
                              <SelectItem value="active">{translations['health.active']}</SelectItem>
                              <SelectItem value="extremely_active">{translations['health.extremely_active']}</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <Button onClick={calculateCalories} className="w-full bg-green-600 hover:bg-green-700">
                        {translations['common.calculate']}
                      </Button>
                    </CardContent>
                  </Card>

                  {calorieResult && (
                    <Card className="shadow-lg w-full lg:w-1/2">
                      <CardHeader>
                        <CardTitle className={isRTL ? 'text-right' : ''}>{translations['health.calories_result']}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <div className={`text-sm text-blue-600 ${isRTL ? 'text-right' : ''}`}>BMR</div>
                            <div className={`text-2xl font-bold text-blue-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.bmr)}
                            </div>
                            <div className={`text-xs text-blue-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.calories_at_rest']}</div>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <div className={`text-sm text-green-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.maintenance']}</div>
                            <div className={`text-2xl font-bold text-green-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.maintenance)}
                            </div>
                            <div className={`text-xs text-green-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.calories_maintain']}</div>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <div className={`text-sm text-orange-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.weight_loss']}</div>
                            <div className={`text-2xl font-bold text-orange-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.loseWeight)}
                            </div>
                            <div className={`text-xs text-orange-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.calories_lose']}</div>
                          </div>
                          <div className="p-4 bg-purple-50 rounded-lg">
                            <div className={`text-sm text-purple-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.weight_gain']}</div>
                            <div className={`text-2xl font-bold text-purple-800 ${isRTL ? 'text-right' : ''}`}>
                              {Math.round(calorieResult.gainWeight)}
                            </div>
                            <div className={`text-xs text-purple-600 ${isRTL ? 'text-right' : ''}`}>{translations['health.calories_gain']}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>
            </Tabs>

            {/* Medical Disclaimer */}
            <Alert className="mt-8 border-pink-200 bg-pink-50">
              <Shield className="h-4 w-4 text-pink-600" />
              <AlertDescription className="text-pink-800">
                {translations['health.seo.disclaimer']}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* SEO Content Section - Moved below the calculator */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`text-center mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {translations['health.seo.intro']}
            </p>
          </div>
          
          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${isRTL ? 'text-right' : 'text-left'}`}>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {translations['health.seo.calculators_title']}
              </h3>
              <div className="space-y-4">
                {[
                  { icon: Scale, key: 'health.seo.calculator1' },
                  { icon: Activity, key: 'health.seo.calculator2' },
                  { icon: Target, key: 'health.seo.calculator3' },
                  { icon: Zap, key: 'health.seo.calculator4' }
                ].map((calculator, index) => {
                  const Icon = calculator.icon;
                  return (
                    <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className="h-6 w-6 text-pink-600 mt-1 flex-shrink-0" />
                      <p className="text-gray-700">{translations[calculator.key]}</p>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-pink-50 to-rose-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Shield className="h-6 w-6 text-pink-600" />
                Medical Disclaimer
              </h3>
              <p className="text-gray-700 mb-4">
                {translations['health.seo.disclaimer']}
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">BMI Assessment</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Calorie Planning</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Fitness Tracking</Badge>
                <Badge variant="secondary" className="bg-pink-100 text-pink-800">Health Monitoring</Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthCalculator;
