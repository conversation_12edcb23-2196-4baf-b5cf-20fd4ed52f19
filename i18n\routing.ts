import {defineRouting} from 'next-intl/routing';
import {createNavigation} from 'next-intl/navigation';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'fr', 'es', 'ar'],

  // Used when no locale matches
  defaultLocale: 'en',

  // The pathnames configuration
  pathnames: {
    '/': '/',
    '/scientific': {
      en: '/scientific',
      fr: '/scientifique',
      es: '/cientifica',
      ar: '/علمية'
    },
    '/financial': {
      en: '/financial',
      fr: '/financiere',
      es: '/financiera',
      ar: '/مالية'
    },
    '/conversion': {
      en: '/conversion',
      fr: '/conversion',
      es: '/conversion',
      ar: '/تحويل'
    },
    '/health': {
      en: '/health',
      fr: '/sante',
      es: '/salud',
      ar: '/صحة'
    }
  }
});

export const locales = routing.locales;
export type Locale = typeof locales[number];

export const defaultLocale = routing.defaultLocale;

export const localeNames: Record<Locale, string> = {
  en: 'English',
  fr: 'Français',
  es: 'Español',
  ar: 'العربية'
};

export const pathnames = routing.pathnames;

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const {Link, redirect, usePathname, useRouter} =
  createNavigation(routing);
