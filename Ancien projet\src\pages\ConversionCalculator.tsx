
import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { CheckCircle, Calculator, Zap, Globe, Users } from 'lucide-react';

const ConversionCalculator = () => {
  const { translations, isRTL } = useLanguage();
  const [category, setCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('');
  const [toUnit, setToUnit] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');

  const conversions: Record<string, Record<string, { name: string; factor: number }>> = {
    length: {
      meter: { name: translations['units.meter'], factor: 1 },
      kilometer: { name: translations['units.kilometer'], factor: 1000 },
      centimeter: { name: translations['units.centimeter'], factor: 0.01 },
      millimeter: { name: translations['units.millimeter'], factor: 0.001 },
      inch: { name: translations['units.inch'], factor: 0.0254 },
      foot: { name: translations['units.foot'], factor: 0.3048 },
      yard: { name: translations['units.yard'], factor: 0.9144 },
      mile: { name: translations['units.mile'], factor: 1609.34 },
    },
    weight: {
      kilogram: { name: translations['units.kilogram'], factor: 1 },
      gram: { name: translations['units.gram'], factor: 0.001 },
      pound: { name: translations['units.pound'], factor: 0.453592 },
      ounce: { name: translations['units.ounce'], factor: 0.0283495 },
      ton: { name: translations['units.ton'], factor: 1000 },
    },
    temperature: {
      celsius: { name: translations['units.celsius'], factor: 1 },
      fahrenheit: { name: translations['units.fahrenheit'], factor: 1 },
      kelvin: { name: translations['units.kelvin'], factor: 1 },
    },
    area: {
      square_meter: { name: translations['units.square_meter'], factor: 1 },
      square_kilometer: { name: translations['units.square_kilometer'], factor: 1000000 },
      square_centimeter: { name: translations['units.square_centimeter'], factor: 0.0001 },
      square_inch: { name: translations['units.square_inch'], factor: 0.00064516 },
      square_foot: { name: translations['units.square_foot'], factor: 0.092903 },
      acre: { name: translations['units.acre'], factor: 4046.86 },
      hectare: { name: translations['units.hectare'], factor: 10000 },
    },
    volume: {
      liter: { name: translations['units.liter'], factor: 1 },
      milliliter: { name: translations['units.milliliter'], factor: 0.001 },
      cubic_meter: { name: translations['units.cubic_meter'], factor: 1000 },
      cubic_centimeter: { name: translations['units.cubic_centimeter'], factor: 0.001 },
      gallon: { name: translations['units.gallon'], factor: 3.78541 },
      quart: { name: translations['units.quart'], factor: 0.946353 },
      pint: { name: translations['units.pint'], factor: 0.473176 },
      cup: { name: translations['units.cup'], factor: 0.236588 },
    },
    speed: {
      meter_per_second: { name: translations['units.meter_per_second'], factor: 1 },
      kilometer_per_hour: { name: translations['units.kilometer_per_hour'], factor: 0.277778 },
      mile_per_hour: { name: translations['units.mile_per_hour'], factor: 0.44704 },
      foot_per_second: { name: translations['units.foot_per_second'], factor: 0.3048 },
      knot: { name: translations['units.knot'], factor: 0.514444 },
    },
    energy: {
      joule: { name: translations['units.joule'], factor: 1 },
      kilojoule: { name: translations['units.kilojoule'], factor: 1000 },
      calorie: { name: translations['units.calorie'], factor: 4.184 },
      kilocalorie: { name: translations['units.kilocalorie'], factor: 4184 },
      watt_hour: { name: translations['units.watt_hour'], factor: 3600 },
      kilowatt_hour: { name: translations['units.kilowatt_hour'], factor: 3600000 },
    },
    pressure: {
      pascal: { name: translations['units.pascal'], factor: 1 },
      kilopascal: { name: translations['units.kilopascal'], factor: 1000 },
      bar: { name: translations['units.bar'], factor: 100000 },
      atmosphere: { name: translations['units.atmosphere'], factor: 101325 },
      psi: { name: translations['units.psi'], factor: 6894.76 },
      torr: { name: translations['units.torr'], factor: 133.322 },
    },
    power: {
      watt: { name: translations['units.watt'], factor: 1 },
      kilowatt: { name: translations['units.kilowatt'], factor: 1000 },
      horsepower: { name: translations['units.horsepower'], factor: 745.7 },
      btu_per_hour: { name: translations['units.btu_per_hour'], factor: 0.293071 },
    },
  };

  const handleConvert = () => {
    if (!inputValue || !fromUnit || !toUnit) return;

    const value = parseFloat(inputValue);
    if (isNaN(value)) return;

    let convertedValue: number;

    if (category === 'temperature') {
      // Special handling for temperature conversions
      if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {
        convertedValue = (value * 9/5) + 32;
      } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {
        convertedValue = (value - 32) * 5/9;
      } else if (fromUnit === 'celsius' && toUnit === 'kelvin') {
        convertedValue = value + 273.15;
      } else if (fromUnit === 'kelvin' && toUnit === 'celsius') {
        convertedValue = value - 273.15;
      } else if (fromUnit === 'fahrenheit' && toUnit === 'kelvin') {
        convertedValue = ((value - 32) * 5/9) + 273.15;
      } else if (fromUnit === 'kelvin' && toUnit === 'fahrenheit') {
        convertedValue = ((value - 273.15) * 9/5) + 32;
      } else {
        convertedValue = value; // Same unit
      }
    } else {
      // Regular unit conversions using factors
      const fromFactor = conversions[category][fromUnit]?.factor || 1;
      const toFactor = conversions[category][toUnit]?.factor || 1;
      convertedValue = (value * fromFactor) / toFactor;
    }

    setResult(convertedValue.toFixed(6));
  };

  const categories = [
    { key: 'length', name: translations['conversion.length'] },
    { key: 'weight', name: translations['conversion.weight'] },
    { key: 'temperature', name: translations['conversion.temperature'] },
    { key: 'area', name: translations['conversion.area'] },
    { key: 'volume', name: translations['conversion.volume'] },
    { key: 'speed', name: translations['conversion.speed'] },
    { key: 'energy', name: translations['conversion.energy'] },
    { key: 'pressure', name: translations['conversion.pressure'] },
    { key: 'power', name: translations['conversion.power'] },
  ];

  // Reset units when category changes
  React.useEffect(() => {
    setFromUnit('');
    setToUnit('');
    setResult('');
  }, [category]);

  const categoryFeatures = [
    { icon: CheckCircle, key: 'conversion.seo.category1' },
    { icon: Calculator, key: 'conversion.seo.category2' },
    { icon: Zap, key: 'conversion.seo.category3' },
    { icon: Globe, key: 'conversion.seo.category4' },
    { icon: Users, key: 'conversion.seo.category5' },
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {translations['conversion.title']}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-600 mx-auto rounded-full" />
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              {translations['conversion.title']}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Category Selection */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {translations['conversion.category']}
              </label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {categories.map((cat) => (
                    <SelectItem key={cat.key} value={cat.key} className={isRTL ? 'text-right' : 'text-left'}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Input Value */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {translations['conversion.value']}
              </label>
              <Input
                type="number"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={translations['conversion.enter_value']}
                className={isRTL ? 'text-right' : 'text-left'}
              />
            </div>

            {/* From Unit */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {translations['conversion.from']}
              </label>
              <Select value={fromUnit} onValueChange={setFromUnit}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {Object.entries(conversions[category] || {}).map(([key, unit]) => (
                    <SelectItem key={key} value={key} className={isRTL ? 'text-right' : 'text-left'}>
                      {unit.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* To Unit */}
            <div>
              <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                {translations['conversion.to']}
              </label>
              <Select value={toUnit} onValueChange={setToUnit}>
                <SelectTrigger className={isRTL ? 'text-right' : 'text-left'}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent dir={isRTL ? 'rtl' : 'ltr'} className="bg-white border border-gray-200 shadow-lg z-50">
                  {Object.entries(conversions[category] || {}).map(([key, unit]) => (
                    <SelectItem key={key} value={key} className={isRTL ? 'text-right' : 'text-left'}>
                      {unit.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleConvert} className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700">
              {translations['common.calculate']}
            </Button>

            {result && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {translations['common.result']}
                </h3>
                <p className={`text-2xl font-bold text-orange-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {result}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* SEO Content Section - Moved below the calculator */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {translations['conversion.seo.intro']}
            </p>
            
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {translations['conversion.seo.categories_title']}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {categoryFeatures.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className={`flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Icon className="h-6 w-6 text-orange-500 mt-1 flex-shrink-0" />
                    <p className="text-gray-700">{translations[feature.key]}</p>
                  </div>
                );
              })}
            </div>
            
            <p className="text-gray-600 italic">
              {translations['conversion.seo.applications']}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversionCalculator;
