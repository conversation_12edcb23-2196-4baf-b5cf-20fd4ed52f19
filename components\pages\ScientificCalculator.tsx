'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Calculator, History, Settings, HardDrive, Cpu, Zap, BookOpen, TrendingUp, Users, Target, Copy, RotateCcw, Archive, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

const ScientificCalculator = () => {
  const t = useTranslations('scientific');
  const locale = useLocale();
  const isRTL = locale === 'ar';

  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<string | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForNewValue, setWaitingForNewValue] = useState(false);
  const [memory, setMemory] = useState(0);
  const [history, setHistory] = useState<string[]>([]);
  const [angleMode, setAngleMode] = useState<'deg' | 'rad'>('deg');

  // Keyboard support
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;

      if (key >= '0' && key <= '9') {
        inputNumber(key);
      } else if (key === '.') {
        inputNumber('.');
      } else if (key === '+') {
        inputOperation('+');
      } else if (key === '-') {
        inputOperation('-');
      } else if (key === '*') {
        inputOperation('×');
      } else if (key === '/') {
        event.preventDefault();
        inputOperation('÷');
      } else if (key === 'Enter' || key === '=') {
        performCalculation();
      } else if (key === 'Escape' || key === 'c' || key === 'C') {
        clear();
      } else if (key === 'Backspace') {
        setDisplay(display.slice(0, -1) || '0');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [display]);

  const inputNumber = useCallback((num: string) => {
    if (waitingForNewValue) {
      setDisplay(num);
      setWaitingForNewValue(false);
    } else {
      if (num === '.' && display.includes('.')) return;
      setDisplay(display === '0' ? num : display + num);
    }
  }, [display, waitingForNewValue]);

  const inputOperation = useCallback((nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(display);
    } else if (operation) {
      const currentValue = previousValue || '0';
      const newValue = calculate(parseFloat(currentValue), inputValue, operation);

      const historyEntry = `${currentValue} ${operation} ${inputValue} = ${newValue}`;
      setHistory(prev => [historyEntry, ...prev.slice(0, 19)]);

      setDisplay(String(newValue));
      setPreviousValue(String(newValue));
    }

    setWaitingForNewValue(true);
    setOperation(nextOperation);
  }, [display, previousValue, operation]);

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+': return firstValue + secondValue;
      case '-': return firstValue - secondValue;
      case '×': return firstValue * secondValue;
      case '÷': return secondValue !== 0 ? firstValue / secondValue : NaN;
      case '^': return Math.pow(firstValue, secondValue);
      case 'mod': return firstValue % secondValue;
      case 'xroot': return Math.pow(secondValue, 1 / firstValue);
      default: return secondValue;
    }
  };

  const performUnaryOperation = useCallback((op: string) => {
    const currentValue = parseFloat(display);
    let result: number;

    const angleMultiplier = angleMode === 'deg' ? Math.PI / 180 : 1;

    switch (op) {
      case '√': result = Math.sqrt(currentValue); break;
      case '∛': result = Math.cbrt(currentValue); break;
      case 'x²': result = Math.pow(currentValue, 2); break;
      case 'x³': result = Math.pow(currentValue, 3); break;
      case '1/x': result = currentValue !== 0 ? 1 / currentValue : NaN; break;
      case 'sin': result = Math.sin(currentValue * angleMultiplier); break;
      case 'cos': result = Math.cos(currentValue * angleMultiplier); break;
      case 'tan': result = Math.tan(currentValue * angleMultiplier); break;
      case 'asin': result = Math.asin(currentValue) / angleMultiplier; break;
      case 'acos': result = Math.acos(currentValue) / angleMultiplier; break;
      case 'atan': result = Math.atan(currentValue) / angleMultiplier; break;
      case 'sinh': result = Math.sinh(currentValue); break;
      case 'cosh': result = Math.cosh(currentValue); break;
      case 'tanh': result = Math.tanh(currentValue); break;
      case 'ln': result = Math.log(currentValue); break;
      case 'log': result = Math.log10(currentValue); break;
      case 'log2': result = Math.log2(currentValue); break;
      case 'exp': result = Math.exp(currentValue); break;
      case '10^x': result = Math.pow(10, currentValue); break;
      case '2^x': result = Math.pow(2, currentValue); break;
      case '!': result = factorial(currentValue); break;
      case '+/-': result = -currentValue; break;
      case 'abs': result = Math.abs(currentValue); break;
      case 'floor': result = Math.floor(currentValue); break;
      case 'ceil': result = Math.ceil(currentValue); break;
      case 'round': result = Math.round(currentValue); break;
      default: return;
    }

    const historyEntry = `${op}(${currentValue}) = ${result}`;
    setHistory(prev => [historyEntry, ...prev.slice(0, 19)]);

    setDisplay(isNaN(result) ? t('error') || 'Error' : String(result));
    setWaitingForNewValue(true);
  }, [display, angleMode, t]);

  const factorial = (n: number): number => {
    if (n < 0 || !Number.isInteger(n) || n > 170) return NaN;
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  };

  const insertConstant = useCallback((constant: string) => {
    let value: number;
    switch (constant) {
      case 'pi':
        value = Math.PI;
        break;
      case 'e':
        value = Math.E;
        break;
      default:
        return;
    }
    setDisplay(String(value));
    setWaitingForNewValue(true);
  }, []);

  const clear = useCallback(() => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForNewValue(false);
  }, []);

  const clearHistory = useCallback(() => {
    setHistory([]);
    toast({
      title: t('history_cleared'),
      description: t('history_cleared_desc'),
    });
  }, [t]);

  const copyResult = useCallback(() => {
    navigator.clipboard.writeText(display);
    toast({
      title: t('copied'),
      description: t('copied_desc'),
    });
  }, [display, t]);

  // Memory functions
  const memoryStore = useCallback(() => {
    setMemory(parseFloat(display));
    toast({
      title: t('memory_stored'),
      description: t('memory_stored_desc', { value: display }),
    });
  }, [display, t]);

  const memoryRecall = useCallback(() => {
    setDisplay(String(memory));
    setWaitingForNewValue(true);
  }, [memory]);

  const memoryClear = useCallback(() => {
    setMemory(0);
    toast({
      title: t('memory_cleared'),
      description: t('memory_cleared_desc'),
    });
  }, [t]);

  const memoryAdd = useCallback(() => {
    setMemory(prev => prev + parseFloat(display));
    toast({
      title: t('memory_added'),
      description: t('memory_added_desc', { value: display }),
    });
  }, [display, t]);

  const memorySubtract = useCallback(() => {
    setMemory(prev => prev - parseFloat(display));
    toast({
      title: t('memory_subtracted'),
      description: t('memory_subtracted_desc', { value: display }),
    });
  }, [display, t]);

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('seo.intro')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Calculator */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  {t('calculator')}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant={angleMode === 'deg' ? 'default' : 'secondary'}>
                    {t('angle_mode', { mode: angleMode === 'deg' ? t('degree') : t('radian') })}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAngleMode(angleMode === 'deg' ? 'rad' : 'deg')}
                  >
                    {angleMode === 'deg' ? 'RAD' : 'DEG'}
                  </Button>
                  <Button variant="outline" size="sm" onClick={copyResult}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">{t('basic')}</TabsTrigger>
                    <TabsTrigger value="advanced">{t('advanced')}</TabsTrigger>
                    <TabsTrigger value="functions">{t('functions')}</TabsTrigger>
                    <TabsTrigger value="constants">{t('constants')}</TabsTrigger>
                  </TabsList>

                  {/* Display */}
                  <div className="my-6">
                    <div className="bg-gray-900 text-white p-4 rounded-lg">
                      <div className={`text-3xl font-mono ${isRTL ? 'text-left' : 'text-right'} mb-2`}>
                        {display}
                      </div>
                      {operation && previousValue && (
                        <div className={`text-sm text-gray-400 ${isRTL ? 'text-left' : 'text-right'}`}>
                          {previousValue} {operation}
                        </div>
                      )}
                      <div className="flex justify-between items-center mt-2 text-xs text-gray-400">
                        <span>{t('angle_mode', { mode: angleMode === 'deg' ? 'DEG' : 'RAD' })}</span>
                        {memory !== 0 && <span>M: {memory}</span>}
                      </div>
                    </div>
                  </div>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-5 gap-2">
                      {/* Row 1 */}
                      <Button variant="outline" onClick={clear} className="bg-red-50 hover:bg-red-100 text-red-700">
                        C
                      </Button>
                      <Button variant="outline" onClick={() => setDisplay(display.slice(0, -1) || '0')}>
                        ⌫
                      </Button>
                      <Button variant="outline" onClick={() => inputOperation('^')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">
                        x^y
                      </Button>
                      <Button variant="outline" onClick={() => performFunction('sqrt')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">
                        √
                      </Button>
                      <Button variant="outline" onClick={() => inputOperation('÷')} className="bg-orange-100 hover:bg-orange-200 text-orange-800">
                        ÷
                      </Button>

                      {/* Row 2 */}
                      <Button variant="outline" onClick={() => inputNumber('7')}>7</Button>
                      <Button variant="outline" onClick={() => inputNumber('8')}>8</Button>
                      <Button variant="outline" onClick={() => inputNumber('9')}>9</Button>
                      <Button variant="outline" onClick={() => performFunction('square')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">
                        x²
                      </Button>
                      <Button variant="outline" onClick={() => inputOperation('×')} className="bg-orange-100 hover:bg-orange-200 text-orange-800">
                        ×
                      </Button>

                      {/* Row 3 */}
                      <Button variant="outline" onClick={() => inputNumber('4')}>4</Button>
                      <Button variant="outline" onClick={() => inputNumber('5')}>5</Button>
                      <Button variant="outline" onClick={() => inputNumber('6')}>6</Button>
                      <Button variant="outline" onClick={() => performFunction('reciprocal')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">
                        1/x
                      </Button>
                      <Button variant="outline" onClick={() => inputOperation('-')} className="bg-orange-100 hover:bg-orange-200 text-orange-800">
                        -
                      </Button>

                      {/* Row 4 */}
                      <Button variant="outline" onClick={() => inputNumber('1')}>1</Button>
                      <Button variant="outline" onClick={() => inputNumber('2')}>2</Button>
                      <Button variant="outline" onClick={() => inputNumber('3')}>3</Button>
                      <Button variant="outline" onClick={() => performFunction('factorial')} className="bg-blue-50 hover:bg-blue-100 text-blue-700">
                        n!
                      </Button>
                      <Button variant="outline" onClick={() => inputOperation('+')} className="bg-orange-100 hover:bg-orange-200 text-orange-800">
                        +
                      </Button>

                      {/* Row 5 */}
                      <Button variant="outline" onClick={() => inputNumber('0')} className="col-span-2">0</Button>
                      <Button variant="outline" onClick={() => inputNumber('.')}>.</Button>
                      <Button variant="outline" onClick={() => setDisplay(display.startsWith('-') ? display.slice(1) : '-' + display)}>
                        ±
                      </Button>
                      <Button onClick={performCalculation} className="bg-calculator-primary hover:bg-calculator-primary/90 text-white">
                        =
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-4">
                    <div className="grid grid-cols-5 gap-2">
                      {/* Trigonometric functions */}
                      <Button variant="outline" onClick={() => performFunction('sin')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">
                        sin
                      </Button>
                      <Button variant="outline" onClick={() => performFunction('cos')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">
                        cos
                      </Button>
                      <Button variant="outline" onClick={() => performFunction('tan')} className="bg-purple-50 hover:bg-purple-100 text-purple-700">
                        tan
                      </Button>
                      <Button variant="outline" onClick={() => performFunction('ln')} className="bg-green-50 hover:bg-green-100 text-green-700">
                        ln
                      </Button>
                      <Button variant="outline" onClick={() => performFunction('log')} className="bg-green-50 hover:bg-green-100 text-green-700">
                        log
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="functions" className="space-y-4">
                    <div className="grid grid-cols-4 gap-2">
                      {/* Memory functions */}
                      <Button variant="outline" onClick={memoryStore} className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700">
                        MS
                      </Button>
                      <Button variant="outline" onClick={memoryRecall} className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700">
                        MR
                      </Button>
                      <Button variant="outline" onClick={memoryAdd} className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700">
                        M+
                      </Button>
                      <Button variant="outline" onClick={memoryClear} className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700">
                        MC
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="constants" className="space-y-4">
                    <div className="grid grid-cols-3 gap-2">
                      <Button variant="outline" onClick={() => insertConstant('pi')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">
                        π
                      </Button>
                      <Button variant="outline" onClick={() => insertConstant('e')} className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700">
                        e
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* History */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <History className="h-4 w-4" />
                  {t('history')}
                </CardTitle>
                <Button variant="outline" size="sm" onClick={clearHistory}>
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {history.length === 0 ? (
                    <p className="text-sm text-gray-500 text-center py-4">
                      {t('no_history')}
                    </p>
                  ) : (
                    history.map((calc, index) => (
                      <div key={index} className="text-xs bg-gray-50 p-2 rounded font-mono">
                        {calc}
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle>{t('seo.features_title')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <p className="text-gray-600">{t('seo.feature1')}</p>
                  <p className="text-gray-600">{t('seo.feature2')}</p>
                  <p className="text-gray-600">{t('seo.feature3')}</p>
                  <p className="text-gray-600">{t('seo.feature4')}</p>
                  <p className="text-gray-600">{t('seo.feature5')}</p>
                </div>
              </CardContent>
            </Card>

            {/* Use Cases */}
            <Card>
              <CardHeader>
                <CardTitle>Use Cases</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{t('seo.use_cases')}</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
          <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {t('seo.features_title')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700">
              <div>
                <h3 className="font-semibold mb-2">Advanced Mathematical Functions</h3>
                <p className="text-sm">{t('seo.feature1')}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Logarithmic & Exponential</h3>
                <p className="text-sm">{t('seo.feature2')}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Memory Functions</h3>
                <p className="text-sm">{t('seo.feature3')}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Calculation History</h3>
                <p className="text-sm">{t('seo.feature4')}</p>
              </div>
            </div>
            <div className="mt-6 pt-6 border-t">
              <h3 className="font-semibold mb-2">Perfect For:</h3>
              <p className="text-sm text-gray-600">{t('seo.use_cases')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScientificCalculator;
