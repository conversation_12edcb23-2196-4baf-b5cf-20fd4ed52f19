'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const ScientificCalculator = () => {
  const t = useTranslations('scientific');
  const locale = useLocale();
  const isRTL = locale === 'ar';

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'font-arabic' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('seo.intro')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Calculator */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>{t('calculator')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-8 rounded-lg text-center">
                  <p className="text-gray-600">
                    {t('seo.intro')}
                  </p>
                  <p className="text-sm text-gray-500 mt-4">
                    Scientific calculator functionality will be implemented here.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Features */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('seo.features_title')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">{t('seo.feature1')}</p>
                  <p className="text-sm text-gray-600">{t('seo.feature2')}</p>
                  <p className="text-sm text-gray-600">{t('seo.feature3')}</p>
                  <p className="text-sm text-gray-600">{t('seo.feature4')}</p>
                  <p className="text-sm text-gray-600">{t('seo.feature5')}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Use Cases</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{t('seo.use_cases')}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScientificCalculator;
